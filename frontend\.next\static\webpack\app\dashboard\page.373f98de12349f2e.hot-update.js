"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/DashboardTabs.tsx":
/*!****************************************************!*\
  !*** ./src/components/dashboard/DashboardTabs.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardTabs)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./src/components/ui/tabs.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_History_ListOrdered_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=History,ListOrdered!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/list-ordered.js\");\n/* harmony import */ var _barrel_optimize_names_History_ListOrdered_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=History,ListOrdered!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/history.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst tabConfig = [\n    {\n        value: \"orders\",\n        label: \"Orders\",\n        href: \"/dashboard\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_History_ListOrdered_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardTabs.tsx\",\n            lineNumber: 9,\n            columnNumber: 65\n        }, undefined)\n    },\n    {\n        value: \"history\",\n        label: \"History\",\n        href: \"/dashboard/history\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_History_ListOrdered_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardTabs.tsx\",\n            lineNumber: 10,\n            columnNumber: 75\n        }, undefined)\n    }\n];\nfunction DashboardTabs() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    // Determine active tab based on pathname\n    let activeTabValue = \"orders\"; // Default\n    if (pathname === \"/dashboard/history\") activeTabValue = \"history\";\n    const onTabChange = (value)=>{\n        const tab = tabConfig.find((t)=>t.value === value);\n        if (tab) {\n            router.push(tab.href);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_2__.Tabs, {\n        value: activeTabValue,\n        onValueChange: onTabChange,\n        className: \"w-full mb-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_2__.TabsList, {\n            className: \"grid w-full grid-cols-3 bg-card border-2 border-border\",\n            children: tabConfig.map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_2__.TabsTrigger, {\n                    value: tab.value,\n                    className: \"text-base data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=active]:font-bold\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            tab.icon,\n                            tab.label\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardTabs.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 13\n                    }, this)\n                }, tab.value, false, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardTabs.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 11\n                }, this))\n        }, void 0, false, {\n            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardTabs.tsx\",\n            lineNumber: 31,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardTabs.tsx\",\n        lineNumber: 30,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardTabs, \"gA9e4WsoP6a20xDgQgrFkfMP8lc=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname\n    ];\n});\n_c = DashboardTabs;\nvar _c;\n$RefreshReg$(_c, \"DashboardTabs\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/DashboardTabs.tsx\n"));

/***/ })

});