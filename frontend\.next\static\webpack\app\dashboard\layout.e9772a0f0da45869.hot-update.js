"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/layout",{

/***/ "(app-pages-browser)/./src/lib/session-manager.ts":
/*!************************************!*\
  !*** ./src/lib/session-manager.ts ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SessionManager: () => (/* binding */ SessionManager)\n/* harmony export */ });\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! uuid */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/v4.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n\n\nconst SESSIONS_STORAGE_KEY = 'pluto_trading_sessions';\nconst CURRENT_SESSION_KEY = 'pluto_current_session';\n// Generate a unique window ID for this browser tab/window\nconst generateWindowId = ()=>{\n    return \"window_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substr(2, 9));\n};\n// Get or create window ID for this tab\nconst getWindowId = ()=>{\n    if (false) {}\n    // Use sessionStorage (tab-specific) instead of localStorage (shared across tabs)\n    let windowId = sessionStorage.getItem('pluto_window_id');\n    if (!windowId) {\n        windowId = generateWindowId();\n        sessionStorage.setItem('pluto_window_id', windowId);\n        console.log(\"\\uD83C\\uDD95 Created new window ID: \".concat(windowId));\n    } else {\n        console.log(\"\\uD83D\\uDD04 Using existing window ID: \".concat(windowId));\n    }\n    return windowId;\n};\nclass SessionManager {\n    static getInstance() {\n        if (!SessionManager.instance) {\n            SessionManager.instance = new SessionManager();\n        }\n        return SessionManager.instance;\n    }\n    generateSessionName(config) {\n        const crypto1 = config.crypto1 || 'Crypto1';\n        const crypto2 = config.crypto2 || 'Crypto2';\n        const tradingMode = config.tradingMode || 'SimpleSpot';\n        const baseName = \"\".concat(crypto1, \"/\").concat(crypto2, \" \").concat(tradingMode);\n        // Check for existing sessions with the same base name\n        const existingSessions = Array.from(this.sessions.values());\n        const similarSessions = existingSessions.filter((session)=>session.name.startsWith(baseName));\n        if (similarSessions.length === 0) {\n            return baseName;\n        }\n        // Find the highest session number\n        let maxNumber = 0;\n        similarSessions.forEach((session)=>{\n            const match = session.name.match(new RegExp(\"^\".concat(baseName.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&'), \" Session (\\\\d+)$\")));\n            if (match) {\n                const number = parseInt(match[1], 10);\n                if (number > maxNumber) {\n                    maxNumber = number;\n                }\n            } else if (session.name === baseName) {\n                // If there's an exact match, treat it as \"Session 1\"\n                maxNumber = Math.max(maxNumber, 1);\n            }\n        });\n        return \"\".concat(baseName, \" Session \").concat(maxNumber + 1);\n    }\n    async initializeBackendConnection() {\n        // Prevent multiple initialization attempts\n        if (this.isInitializing) {\n            console.log('⚠️ Backend initialization already in progress, skipping');\n            return;\n        }\n        this.isInitializing = true;\n        try {\n            // Check if user is authenticated\n            const authToken = localStorage.getItem('plutoAuthToken');\n            if (!authToken) {\n                console.log('⚠️ No auth token found, using localStorage mode until login');\n                this.useBackend = false;\n                this.loadSessionsFromStorage();\n                return;\n            }\n            // Test backend connection (health endpoint doesn't need auth)\n            const response = await fetch('http://localhost:5000/health/', {\n                method: 'GET',\n                headers: {\n                    'Content-Type': 'application/json'\n                }\n            });\n            if (response.ok) {\n                console.log('✅ Backend connection established, testing auth and loading sessions');\n                this.useBackend = true;\n                // Load sessions from backend when connection is established\n                await this.loadSessionsFromBackend();\n            } else {\n                throw new Error('Backend health check failed');\n            }\n        } catch (error) {\n            console.log('⚠️ Backend not available, using localStorage mode:', error);\n            this.useBackend = false;\n            this.loadSessionsFromStorage();\n        } finally{\n            this.isInitializing = false;\n        }\n    }\n    async checkBackendConnection() {\n        try {\n            const authToken = localStorage.getItem('plutoAuthToken');\n            if (!authToken) {\n                return false;\n            }\n            const response = await fetch('http://localhost:5000/health/', {\n                method: 'GET',\n                headers: {\n                    'Content-Type': 'application/json',\n                    'Authorization': \"Bearer \".concat(authToken)\n                }\n            });\n            return response.ok;\n        } catch (error) {\n            console.log('⚠️ Backend connection check failed:', error);\n            return false;\n        }\n    }\n    getWindowSpecificKey(baseKey) {\n        return \"\".concat(baseKey, \"_\").concat(this.windowId);\n    }\n    setupStorageListener() {\n        if (false) {}\n        // Listen for storage changes from other windows\n        window.addEventListener('storage', (event)=>{\n            if (event.key === SESSIONS_STORAGE_KEY && event.newValue) {\n                try {\n                    // Reload sessions when they change in another window\n                    const parsedSessions = JSON.parse(event.newValue);\n                    this.sessions = new Map(Object.entries(parsedSessions));\n                    console.log(\"\\uD83D\\uDD04 Sessions synced from another window (\".concat(this.sessions.size, \" sessions)\"));\n                } catch (error) {\n                    console.error('Failed to sync sessions from storage event:', error);\n                }\n            }\n        });\n    }\n    loadSessionsFromStorage() {\n        try {\n            if (false) {}\n            // Load sessions from shared storage (all windows see same sessions)\n            const sessionsData = localStorage.getItem(SESSIONS_STORAGE_KEY);\n            // Try to load current session from multiple sources for better persistence\n            let currentSessionId = null;\n            // 1. First try window-specific storage (for new tabs)\n            const windowSpecificKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n            currentSessionId = localStorage.getItem(windowSpecificKey);\n            // 2. If not found, try sessionStorage (survives page refresh)\n            if (!currentSessionId) {\n                currentSessionId = sessionStorage.getItem(CURRENT_SESSION_KEY);\n            }\n            // 3. If still not found, look for any active session (fallback)\n            if (!currentSessionId && sessionsData) {\n                const parsedSessions = JSON.parse(sessionsData);\n                const activeSessions = Object.entries(parsedSessions).filter((param)=>{\n                    let [_, session] = param;\n                    return session.isActive && session.lastModified && Date.now() - session.lastModified < 30 * 60 * 1000 // Active within last 30 minutes\n                    ;\n                });\n                if (activeSessions.length > 0) {\n                    // Get the most recently active session\n                    const mostRecentSession = activeSessions.reduce((latest, current)=>current[1].lastModified > latest[1].lastModified ? current : latest);\n                    currentSessionId = mostRecentSession[0];\n                    console.log(\"\\uD83D\\uDD04 Restored most recent active session: \".concat(currentSessionId));\n                }\n            }\n            if (sessionsData) {\n                const parsedSessions = JSON.parse(sessionsData);\n                this.sessions = new Map(Object.entries(parsedSessions));\n            }\n            this.currentSessionId = currentSessionId;\n            // Store current session in sessionStorage for page refresh persistence\n            if (currentSessionId) {\n                sessionStorage.setItem(CURRENT_SESSION_KEY, currentSessionId);\n            }\n            console.log(\"\\uD83D\\uDCC2 Loaded \".concat(this.sessions.size, \" shared sessions for window \").concat(this.windowId, \", current: \").concat(currentSessionId));\n        } catch (error) {\n            console.error('Failed to load sessions from storage:', error);\n        }\n    }\n    async loadSessionsFromBackend() {\n        try {\n            // Check if user is authenticated before making API calls\n            const token =  true ? localStorage.getItem('plutoAuthToken') : 0;\n            if (!token || token.length < 10) {\n                console.log('⚠️ Invalid or missing auth token, skipping backend session loading');\n                this.useBackend = false; // Disable backend mode\n                this.loadSessionsFromStorage();\n                return;\n            }\n            console.log('🔄 Loading sessions from backend...');\n            const { sessionApi } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\"));\n            const response = await sessionApi.getAllSessions(true);\n            console.log('✅ Backend response received:', response);\n            if (response && response.sessions) {\n                // Convert backend sessions to our internal format\n                this.sessions.clear();\n                response.sessions.forEach((session)=>{\n                    const sessionData = {\n                        id: session.session_uuid,\n                        name: session.name,\n                        config: JSON.parse(session.config_snapshot || '{}'),\n                        createdAt: new Date(session.created_at).getTime(),\n                        lastModified: new Date(session.last_modified).getTime(),\n                        isActive: session.is_active,\n                        runtime: session.runtime || 0,\n                        targetPriceRows: session.target_price_rows ? JSON.parse(session.target_price_rows) : [],\n                        orderHistory: session.order_history ? JSON.parse(session.order_history) : [],\n                        currentMarketPrice: session.current_market_price || 100000,\n                        crypto1Balance: session.crypto1_balance || 10000,\n                        crypto2Balance: session.crypto2_balance || 10000,\n                        stablecoinBalance: session.stablecoin_balance || 10000,\n                        alarmSettings: session.alarm_settings ? JSON.parse(session.alarm_settings) : undefined\n                    };\n                    this.sessions.set(session.session_uuid, sessionData);\n                });\n                // Find active session\n                const activeSession = response.sessions.find((s)=>s.is_active);\n                if (activeSession) {\n                    this.currentSessionId = activeSession.session_uuid;\n                }\n                console.log(\"\\uD83D\\uDCC2 Loaded \".concat(this.sessions.size, \" sessions from backend\"));\n            }\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : String(error);\n            // Handle authentication errors gracefully\n            if (errorMessage.includes('Authentication') || errorMessage.includes('401') || errorMessage.includes('422')) {\n                console.log('🔐 Authentication issue detected, disabling backend mode');\n                this.useBackend = false; // Disable backend mode to prevent future API calls\n            } else if (errorMessage.includes('Cannot connect to server')) {\n                console.log('🌐 Backend server not available, using local storage only');\n            } else {\n                // Only log detailed errors for unexpected issues\n                console.warn('⚠️ Backend session loading failed, falling back to local storage:', errorMessage);\n            }\n            // Fallback to localStorage\n            this.loadSessionsFromStorage();\n        }\n    }\n    saveSessionsToStorage() {\n        try {\n            if (false) {}\n            // Save sessions to shared storage (all windows see same sessions)\n            const sessionsObject = Object.fromEntries(this.sessions);\n            localStorage.setItem(SESSIONS_STORAGE_KEY, JSON.stringify(sessionsObject));\n            // Save current session to window-specific storage\n            const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n            if (this.currentSessionId) {\n                localStorage.setItem(currentSessionKey, this.currentSessionId);\n            }\n        } catch (error) {\n            console.error('Failed to save sessions to storage:', error);\n        }\n    }\n    async createNewSessionWithAutoName(config, customName, currentBalances) {\n        const sessionName = customName || this.generateSessionName(config);\n        return this.createNewSession(sessionName, config, currentBalances);\n    }\n    async createNewSession(name, config, currentBalances) {\n        // Use provided balances or default values\n        const balances = currentBalances || {\n            crypto1: 10,\n            crypto2: 100000,\n            stablecoin: 0\n        };\n        if (this.useBackend) {\n            try {\n                const sessionData = {\n                    name,\n                    config: config,\n                    targetPriceRows: [],\n                    currentMarketPrice: 100000,\n                    crypto1Balance: balances.crypto1,\n                    crypto2Balance: balances.crypto2,\n                    stablecoinBalance: balances.stablecoin\n                };\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_0__.sessionApi.createSession(sessionData);\n                // Add the session to our local cache\n                const newSession = {\n                    id: response.session.session_uuid,\n                    name: response.session.name,\n                    config,\n                    createdAt: new Date(response.session.created_at).getTime(),\n                    lastModified: new Date(response.session.last_modified).getTime(),\n                    isActive: response.session.is_active,\n                    runtime: response.session.runtime || 0,\n                    targetPriceRows: [],\n                    orderHistory: [],\n                    currentMarketPrice: 100000,\n                    crypto1Balance: balances.crypto1,\n                    crypto2Balance: balances.crypto2,\n                    stablecoinBalance: balances.stablecoin\n                };\n                this.sessions.set(response.session.session_uuid, newSession);\n                console.log('✅ Session created on backend:', response.session.session_uuid);\n                return response.session.session_uuid;\n            } catch (error) {\n                console.error('❌ Failed to create session on backend, falling back to localStorage:', error);\n                this.useBackend = false;\n            }\n        }\n        // Fallback to localStorage\n        const sessionId = (0,uuid__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n        const now = Date.now();\n        const newSession = {\n            id: sessionId,\n            name,\n            config,\n            targetPriceRows: [],\n            orderHistory: [],\n            currentMarketPrice: 0,\n            crypto1Balance: balances.crypto1,\n            crypto2Balance: balances.crypto2,\n            stablecoinBalance: balances.stablecoin,\n            createdAt: now,\n            lastModified: now,\n            isActive: false,\n            runtime: 0\n        };\n        this.sessions.set(sessionId, newSession);\n        this.saveSessionsToStorage();\n        return sessionId;\n    }\n    async saveSession(sessionId, config, targetPriceRows, orderHistory, currentMarketPrice, crypto1Balance, crypto2Balance, stablecoinBalance) {\n        let isActive = arguments.length > 8 && arguments[8] !== void 0 ? arguments[8] : false, overrideRuntime = arguments.length > 9 ? arguments[9] : void 0// Optional parameter to set specific runtime\n        ;\n        try {\n            const session = this.sessions.get(sessionId);\n            if (!session) {\n                console.error('Session not found:', sessionId);\n                return false;\n            }\n            // Update runtime - use override if provided, otherwise calculate normally\n            let currentRuntime;\n            if (overrideRuntime !== undefined) {\n                // Use the provided runtime (for saved sessions)\n                currentRuntime = overrideRuntime;\n                console.log(\"\\uD83D\\uDCCA Using override runtime: \".concat(currentRuntime, \"ms for session \").concat(sessionId));\n            } else {\n                // Calculate runtime normally for active sessions\n                currentRuntime = session.runtime || 0;\n                const startTime = this.sessionStartTimes.get(sessionId);\n                if (startTime && isActive) {\n                    // Session is running, update runtime\n                    currentRuntime = (session.runtime || 0) + (Date.now() - startTime);\n                    // Reset start time for next interval\n                    this.sessionStartTimes.set(sessionId, Date.now());\n                } else if (!isActive && startTime) {\n                    // Session stopped, finalize runtime\n                    currentRuntime = (session.runtime || 0) + (Date.now() - startTime);\n                    this.sessionStartTimes.delete(sessionId);\n                } else if (isActive && !startTime) {\n                    // Session just started, record start time\n                    this.sessionStartTimes.set(sessionId, Date.now());\n                }\n            }\n            const updatedSession = {\n                ...session,\n                config,\n                targetPriceRows: [\n                    ...targetPriceRows\n                ],\n                orderHistory: [\n                    ...orderHistory\n                ],\n                currentMarketPrice,\n                crypto1Balance,\n                crypto2Balance,\n                stablecoinBalance,\n                isActive,\n                lastModified: Date.now(),\n                runtime: currentRuntime\n            };\n            this.sessions.set(sessionId, updatedSession);\n            // Save to backend only if explicitly authenticated\n            if (this.useBackend && \"object\" !== 'undefined') {\n                const token = localStorage.getItem('plutoAuthToken');\n                if (token && token.length > 10) {\n                    try {\n                        // Double-check authentication before making API call\n                        const { sessionApi } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\"));\n                        const sessionData = {\n                            name: updatedSession.name,\n                            config: config,\n                            targetPriceRows: targetPriceRows,\n                            currentMarketPrice: currentMarketPrice,\n                            crypto1Balance: crypto1Balance,\n                            crypto2Balance: crypto2Balance,\n                            stablecoinBalance: stablecoinBalance,\n                            isActive: isActive,\n                            additionalRuntime: currentRuntime\n                        };\n                        await sessionApi.updateSession(sessionId, sessionData);\n                        console.log('✅ Session saved to backend:', sessionId);\n                    } catch (error) {\n                        const errorMessage = error instanceof Error ? error.message : String(error);\n                        console.warn('❌ Backend session save failed:', errorMessage);\n                        // Disable backend mode on any authentication-related error\n                        if (errorMessage.includes('401') || errorMessage.includes('422') || errorMessage.includes('Authentication') || errorMessage.includes('required')) {\n                            console.log('🔐 Disabling backend mode due to authentication issue');\n                            this.useBackend = false;\n                        }\n                    // Continue with localStorage save as fallback\n                    }\n                } else {\n                    console.log('⚠️ Invalid or missing auth token, skipping backend session save');\n                    this.useBackend = false; // Disable backend mode if no valid token\n                }\n            }\n            this.saveSessionsToStorage();\n            return true;\n        } catch (error) {\n            console.error('Failed to save session:', error);\n            return false;\n        }\n    }\n    loadSession(sessionId) {\n        return this.sessions.get(sessionId) || null;\n    }\n    async deleteSession(sessionId) {\n        // Delete from backend first if available and user is authenticated\n        if (this.useBackend && \"object\" !== 'undefined') {\n            const token = localStorage.getItem('plutoAuthToken');\n            if (token) {\n                try {\n                    await _lib_api__WEBPACK_IMPORTED_MODULE_0__.sessionApi.deleteSession(sessionId);\n                    console.log('✅ Session deleted from backend:', sessionId);\n                } catch (error) {\n                    const errorMessage = error instanceof Error ? error.message : String(error);\n                    if (errorMessage.includes('401') || errorMessage.includes('422') || errorMessage.includes('Authentication')) {\n                        console.log('🔐 Authentication issue during session deletion, proceeding with local deletion');\n                    } else {\n                        console.error('❌ Failed to delete session from backend:', error);\n                    }\n                // Continue with local deletion as fallback\n                }\n            } else {\n                console.log('⚠️ No auth token, skipping backend session deletion');\n            }\n        }\n        const deleted = this.sessions.delete(sessionId);\n        if (deleted) {\n            if (this.currentSessionId === sessionId) {\n                this.currentSessionId = null;\n                const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n                localStorage.removeItem(currentSessionKey);\n            }\n            this.saveSessionsToStorage();\n        }\n        return deleted;\n    }\n    getAllSessions() {\n        return Array.from(this.sessions.values()).map((session)=>{\n            // Check if this session is active in ANY window by checking all window-specific storage\n            let isActiveInAnyWindow = session.isActive;\n            // Also check if this session is set as current in any window\n            if (true) {\n                try {\n                    // Check all possible window-specific current session keys\n                    for(let i = 0; i < localStorage.length; i++){\n                        const key = localStorage.key(i);\n                        if (key && key.startsWith('pluto_current_session_window_')) {\n                            const windowSessionId = localStorage.getItem(key);\n                            if (windowSessionId === session.id) {\n                                isActiveInAnyWindow = true;\n                                break;\n                            }\n                        }\n                    }\n                } catch (error) {\n                    console.warn('Error checking cross-window session activity:', error);\n                }\n            }\n            return {\n                id: session.id,\n                name: session.name,\n                pair: \"\".concat(session.config.crypto1, \"/\").concat(session.config.crypto2),\n                createdAt: session.createdAt,\n                lastModified: session.lastModified,\n                isActive: isActiveInAnyWindow,\n                runtime: this.getCurrentRuntime(session.id),\n                totalTrades: session.orderHistory.length,\n                totalProfitLoss: session.orderHistory.filter((trade)=>trade.orderType === 'SELL' && trade.realizedProfitLossCrypto2 !== undefined).reduce((sum, trade)=>sum + (trade.realizedProfitLossCrypto2 || 0), 0)\n            };\n        });\n    }\n    setCurrentSession(sessionId) {\n        if (this.sessions.has(sessionId)) {\n            this.currentSessionId = sessionId;\n            // Store in both localStorage (window-specific) and sessionStorage (page refresh persistence)\n            const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n            localStorage.setItem(currentSessionKey, sessionId);\n            sessionStorage.setItem(CURRENT_SESSION_KEY, sessionId);\n            // Mark this session as active when it becomes the current session\n            const session = this.sessions.get(sessionId);\n            if (session && !session.isActive) {\n                session.isActive = true;\n                session.lastModified = Date.now();\n                this.sessions.set(sessionId, session);\n                this.saveSessionsToStorage();\n                console.log(\"✅ Session \".concat(sessionId, \" marked as active for window \").concat(this.windowId));\n            }\n        }\n    }\n    getCurrentSessionId() {\n        return this.currentSessionId;\n    }\n    getAllActiveSessionsAcrossWindows() {\n        const activeSessions = [];\n        if (true) {\n            try {\n                // Check all window-specific current session keys\n                for(let i = 0; i < localStorage.length; i++){\n                    const key = localStorage.key(i);\n                    if (key && key.startsWith('pluto_current_session_window_')) {\n                        const windowSessionId = localStorage.getItem(key);\n                        if (windowSessionId && !activeSessions.includes(windowSessionId)) {\n                            activeSessions.push(windowSessionId);\n                        }\n                    }\n                }\n            } catch (error) {\n                console.warn('Error getting active sessions across windows:', error);\n            }\n        }\n        return activeSessions;\n    }\n    clearCurrentSession() {\n        // Mark current session as inactive before clearing\n        if (this.currentSessionId) {\n            const session = this.sessions.get(this.currentSessionId);\n            if (session && session.isActive) {\n                session.isActive = false;\n                session.lastModified = Date.now();\n                this.sessions.set(this.currentSessionId, session);\n                this.saveSessionsToStorage();\n                console.log(\"⏹️ Session \".concat(this.currentSessionId, \" marked as inactive for window \").concat(this.windowId));\n            }\n        }\n        this.currentSessionId = null;\n        if (true) {\n            const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n            localStorage.removeItem(currentSessionKey);\n            sessionStorage.removeItem(CURRENT_SESSION_KEY);\n        }\n        console.log(\"\\uD83D\\uDDD1️ Cleared current session for window \".concat(this.windowId));\n    }\n    startSessionRuntime(sessionId) {\n        this.sessionStartTimes.set(sessionId, Date.now());\n    }\n    stopSessionRuntime(sessionId) {\n        const startTime = this.sessionStartTimes.get(sessionId);\n        if (startTime) {\n            const session = this.sessions.get(sessionId);\n            if (session) {\n                const additionalRuntime = Date.now() - startTime;\n                session.runtime = (session.runtime || 0) + additionalRuntime;\n                session.lastModified = Date.now();\n                // Keep session active even when runtime stops - only deactivate on manual save or session clear\n                this.sessions.set(sessionId, session);\n                this.saveSessionsToStorage();\n            }\n            this.sessionStartTimes.delete(sessionId);\n        }\n    }\n    deactivateSession(sessionId) {\n        const session = this.sessions.get(sessionId);\n        if (session && session.isActive) {\n            session.isActive = false;\n            session.lastModified = Date.now();\n            this.sessions.set(sessionId, session);\n            this.saveSessionsToStorage();\n            console.log(\"⏹️ Session \".concat(sessionId, \" deactivated\"));\n        }\n    }\n    getCurrentRuntime(sessionId) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return 0;\n        const startTime = this.sessionStartTimes.get(sessionId);\n        if (startTime) {\n            // Session is currently running, add current runtime to stored runtime\n            return (session.runtime || 0) + (Date.now() - startTime);\n        }\n        // Session is not running, return stored runtime\n        return session.runtime || 0;\n    }\n    // Method to refresh backend connection after login/logout\n    async refreshBackendConnection() {\n        console.log('🔄 Refreshing backend connection...');\n        await this.initializeBackendConnection();\n    }\n    // Method to disable backend mode due to authentication issues\n    disableBackendMode() {\n        console.log('🔐 Disabling backend mode due to authentication issues');\n        this.useBackend = false;\n        this.isInitializing = false;\n    }\n    // Method to handle logout - switch to localStorage mode\n    handleLogout() {\n        console.log('👋 User logged out, switching to localStorage mode');\n        this.useBackend = false;\n        this.sessions.clear();\n        this.currentSessionId = null;\n        this.loadSessionsFromStorage();\n    }\n    exportSessionToJSON(sessionId) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return null;\n        return JSON.stringify(session, null, 2);\n    }\n    importSessionFromJSON(jsonData) {\n        try {\n            const sessionData = JSON.parse(jsonData);\n            const sessionId = (0,uuid__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n            const importedSession = {\n                ...sessionData,\n                id: sessionId,\n                isActive: false,\n                lastModified: Date.now()\n            };\n            this.sessions.set(sessionId, importedSession);\n            this.saveSessionsToStorage();\n            return sessionId;\n        } catch (error) {\n            console.error('Failed to import session:', error);\n            return null;\n        }\n    }\n    renameSession(sessionId, newName) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return false;\n        session.name = newName;\n        session.lastModified = Date.now();\n        this.sessions.set(sessionId, session);\n        this.saveSessionsToStorage();\n        return true;\n    }\n    async updateSessionAlarmSettings(sessionId, alarmSettings) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return false;\n        session.alarmSettings = alarmSettings;\n        session.lastModified = Date.now();\n        this.sessions.set(sessionId, session);\n        // Save to backend if available\n        if (this.useBackend) {\n            try {\n                const sessionData = {\n                    name: session.name,\n                    config: session.config,\n                    targetPriceRows: session.targetPriceRows,\n                    currentMarketPrice: session.currentMarketPrice,\n                    crypto1Balance: session.crypto1Balance,\n                    crypto2Balance: session.crypto2Balance,\n                    stablecoinBalance: session.stablecoinBalance,\n                    isActive: session.isActive,\n                    alarm_settings: alarmSettings\n                };\n                await _lib_api__WEBPACK_IMPORTED_MODULE_0__.sessionApi.updateSession(sessionId, sessionData);\n                console.log('✅ Session alarm settings saved to backend:', sessionId);\n            } catch (error) {\n                console.error('❌ Failed to save session alarm settings to backend:', error);\n            // Continue with localStorage save as fallback\n            }\n        }\n        this.saveSessionsToStorage();\n        return true;\n    }\n    getSessionHistory(sessionId) {\n        const session = this.sessions.get(sessionId);\n        return session ? [\n            ...session.orderHistory\n        ] : [];\n    }\n    exportSessionToCSV(sessionId) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return null;\n        const headers = [\n            'Date',\n            'Time',\n            'Pair',\n            'Crypto',\n            'Order Type',\n            'Amount',\n            'Avg Price',\n            'Value',\n            'Price 1',\n            'Crypto 1',\n            'Price 2',\n            'Crypto 2',\n            'Profit/Loss (Crypto1)',\n            'Profit/Loss (Crypto2)'\n        ];\n        const csvContent = [\n            headers.join(','),\n            ...session.orderHistory.map((entry)=>{\n                var _entry_amountCrypto1, _entry_avgPrice, _entry_valueCrypto2, _entry_price1, _entry_price2, _entry_realizedProfitLossCrypto1, _entry_realizedProfitLossCrypto2;\n                return [\n                    new Date(entry.timestamp).toISOString().split('T')[0],\n                    new Date(entry.timestamp).toTimeString().split(' ')[0],\n                    entry.pair,\n                    entry.crypto1Symbol,\n                    entry.orderType,\n                    ((_entry_amountCrypto1 = entry.amountCrypto1) === null || _entry_amountCrypto1 === void 0 ? void 0 : _entry_amountCrypto1.toFixed(session.config.numDigits)) || '',\n                    ((_entry_avgPrice = entry.avgPrice) === null || _entry_avgPrice === void 0 ? void 0 : _entry_avgPrice.toFixed(session.config.numDigits)) || '',\n                    ((_entry_valueCrypto2 = entry.valueCrypto2) === null || _entry_valueCrypto2 === void 0 ? void 0 : _entry_valueCrypto2.toFixed(session.config.numDigits)) || '',\n                    ((_entry_price1 = entry.price1) === null || _entry_price1 === void 0 ? void 0 : _entry_price1.toFixed(session.config.numDigits)) || '',\n                    entry.crypto1Symbol,\n                    ((_entry_price2 = entry.price2) === null || _entry_price2 === void 0 ? void 0 : _entry_price2.toFixed(session.config.numDigits)) || '',\n                    entry.crypto2Symbol,\n                    ((_entry_realizedProfitLossCrypto1 = entry.realizedProfitLossCrypto1) === null || _entry_realizedProfitLossCrypto1 === void 0 ? void 0 : _entry_realizedProfitLossCrypto1.toFixed(session.config.numDigits)) || '',\n                    ((_entry_realizedProfitLossCrypto2 = entry.realizedProfitLossCrypto2) === null || _entry_realizedProfitLossCrypto2 === void 0 ? void 0 : _entry_realizedProfitLossCrypto2.toFixed(session.config.numDigits)) || ''\n                ].join(',');\n            })\n        ].join('\\n');\n        return csvContent;\n    }\n    clearAllSessions() {\n        this.sessions.clear();\n        this.currentSessionId = null;\n        // Clear shared sessions storage\n        localStorage.removeItem(SESSIONS_STORAGE_KEY);\n        // Clear window-specific current session\n        const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n        localStorage.removeItem(currentSessionKey);\n    }\n    // Auto-save functionality\n    enableAutoSave(sessionId, getSessionData) {\n        let intervalMs = arguments.length > 2 && arguments[2] !== void 0 // 30 seconds\n         ? arguments[2] : 30000;\n        const interval = setInterval(()=>{\n            const data = getSessionData();\n            this.saveSession(sessionId, data.config, data.targetPriceRows, data.orderHistory, data.currentMarketPrice, data.crypto1Balance, data.crypto2Balance, data.stablecoinBalance, data.isActive);\n        }, intervalMs);\n        return ()=>clearInterval(interval);\n    }\n    constructor(){\n        this.sessions = new Map();\n        this.currentSessionId = null;\n        this.useBackend = true // Flag to determine if we should use backend API\n        ;\n        this.isInitializing = false // Prevent multiple initialization attempts\n        ;\n        this.sessionStartTimes = new Map() // Track when sessions started running\n        ;\n        this.windowId = getWindowId();\n        console.log(\"\\uD83E\\uDE9F SessionManager initialized for window: \".concat(this.windowId));\n        // Clear any stale session start times on initialization\n        this.sessionStartTimes.clear();\n        // Enable backend by default - we want persistent sessions\n        this.useBackend = true;\n        // Load sessions and setup cross-window sync\n        this.loadSessionsFromStorage();\n        this.setupStorageListener();\n        // Check backend connection and load sessions from backend (with delay to ensure auth is ready)\n        setTimeout(()=>{\n            this.initializeBackendConnection().catch((error)=>{\n                console.warn('⚠️ Failed to initialize backend connection:', error);\n            });\n        }, 1000); // 1 second delay to allow auth to be properly initialized\n        console.log(\"\\uD83E\\uDE9F SessionManager initialized for window \".concat(this.windowId));\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/session-manager.ts\n"));

/***/ })

});