"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_lib_session-manager_ts",{

/***/ "(app-pages-browser)/./src/lib/session-manager.ts":
/*!************************************!*\
  !*** ./src/lib/session-manager.ts ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SessionManager: () => (/* binding */ SessionManager)\n/* harmony export */ });\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! uuid */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/v4.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n\n\nconst SESSIONS_STORAGE_KEY = 'pluto_trading_sessions';\nconst CURRENT_SESSION_KEY = 'pluto_current_session';\n// Generate a unique window ID for this browser tab/window\nconst generateWindowId = ()=>{\n    return \"window_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substr(2, 9));\n};\n// Get or create window ID for this tab\nconst getWindowId = ()=>{\n    if (false) {}\n    // Use sessionStorage (tab-specific) instead of localStorage (shared across tabs)\n    let windowId = sessionStorage.getItem('pluto_window_id');\n    if (!windowId) {\n        windowId = generateWindowId();\n        sessionStorage.setItem('pluto_window_id', windowId);\n        console.log(\"\\uD83C\\uDD95 Created new window ID: \".concat(windowId));\n    } else {\n        console.log(\"\\uD83D\\uDD04 Using existing window ID: \".concat(windowId));\n    }\n    return windowId;\n};\nclass SessionManager {\n    static getInstance() {\n        if (!SessionManager.instance) {\n            SessionManager.instance = new SessionManager();\n        }\n        return SessionManager.instance;\n    }\n    generateSessionName(config) {\n        const crypto1 = config.crypto1 || 'Crypto1';\n        const crypto2 = config.crypto2 || 'Crypto2';\n        const tradingMode = config.tradingMode || 'SimpleSpot';\n        const baseName = \"\".concat(crypto1, \"/\").concat(crypto2, \" \").concat(tradingMode);\n        // Check for existing sessions with the same base name\n        const existingSessions = Array.from(this.sessions.values());\n        const similarSessions = existingSessions.filter((session)=>session.name.startsWith(baseName));\n        if (similarSessions.length === 0) {\n            return baseName;\n        }\n        // Find the highest session number\n        let maxNumber = 0;\n        similarSessions.forEach((session)=>{\n            const match = session.name.match(new RegExp(\"^\".concat(baseName.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&'), \" Session (\\\\d+)$\")));\n            if (match) {\n                const number = parseInt(match[1], 10);\n                if (number > maxNumber) {\n                    maxNumber = number;\n                }\n            } else if (session.name === baseName) {\n                // If there's an exact match, treat it as \"Session 1\"\n                maxNumber = Math.max(maxNumber, 1);\n            }\n        });\n        return \"\".concat(baseName, \" Session \").concat(maxNumber + 1);\n    }\n    async initializeBackendConnection() {\n        // Prevent multiple initialization attempts\n        if (this.isInitializing) {\n            console.log('⚠️ Backend initialization already in progress, skipping');\n            return;\n        }\n        this.isInitializing = true;\n        try {\n            // Check if user is authenticated\n            const authToken = localStorage.getItem('plutoAuthToken');\n            if (!authToken) {\n                console.log('⚠️ No auth token found, using localStorage mode until login');\n                this.useBackend = false;\n                this.loadSessionsFromStorage();\n                return;\n            }\n            // Test backend connection (health endpoint doesn't need auth)\n            const response = await fetch('http://localhost:5000/health/', {\n                method: 'GET',\n                headers: {\n                    'Content-Type': 'application/json'\n                }\n            });\n            if (response.ok) {\n                console.log('✅ Backend connection established, testing auth and loading sessions');\n                this.useBackend = true;\n                // Load sessions from backend when connection is established\n                await this.loadSessionsFromBackend();\n            } else {\n                throw new Error('Backend health check failed');\n            }\n        } catch (error) {\n            console.log('⚠️ Backend not available, using localStorage mode:', error);\n            this.useBackend = false;\n            this.loadSessionsFromStorage();\n        } finally{\n            this.isInitializing = false;\n        }\n    }\n    async checkBackendConnection() {\n        try {\n            const authToken = localStorage.getItem('plutoAuthToken');\n            if (!authToken) {\n                return false;\n            }\n            const response = await fetch('http://localhost:5000/health/', {\n                method: 'GET',\n                headers: {\n                    'Content-Type': 'application/json',\n                    'Authorization': \"Bearer \".concat(authToken)\n                }\n            });\n            return response.ok;\n        } catch (error) {\n            console.log('⚠️ Backend connection check failed:', error);\n            return false;\n        }\n    }\n    getWindowSpecificKey(baseKey) {\n        return \"\".concat(baseKey, \"_\").concat(this.windowId);\n    }\n    setupStorageListener() {\n        if (false) {}\n        // Listen for storage changes from other windows\n        window.addEventListener('storage', (event)=>{\n            if (event.key === SESSIONS_STORAGE_KEY && event.newValue) {\n                try {\n                    // Reload sessions when they change in another window\n                    const parsedSessions = JSON.parse(event.newValue);\n                    this.sessions = new Map(Object.entries(parsedSessions));\n                    console.log(\"\\uD83D\\uDD04 Sessions synced from another window (\".concat(this.sessions.size, \" sessions)\"));\n                } catch (error) {\n                    console.error('Failed to sync sessions from storage event:', error);\n                }\n            }\n        });\n    }\n    loadSessionsFromStorage() {\n        try {\n            if (false) {}\n            // Load sessions from shared storage (all windows see same sessions)\n            const sessionsData = localStorage.getItem(SESSIONS_STORAGE_KEY);\n            // Try to load current session from multiple sources for better persistence\n            let currentSessionId = null;\n            // 1. First try window-specific storage (for new tabs)\n            const windowSpecificKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n            currentSessionId = localStorage.getItem(windowSpecificKey);\n            // 2. If not found, try sessionStorage (survives page refresh)\n            if (!currentSessionId) {\n                currentSessionId = sessionStorage.getItem(CURRENT_SESSION_KEY);\n            }\n            // 3. If still not found, look for any active session (fallback)\n            if (!currentSessionId && sessionsData) {\n                const parsedSessions = JSON.parse(sessionsData);\n                const activeSessions = Object.entries(parsedSessions).filter((param)=>{\n                    let [_, session] = param;\n                    return session.isActive && session.lastModified && Date.now() - session.lastModified < 30 * 60 * 1000 // Active within last 30 minutes\n                    ;\n                });\n                if (activeSessions.length > 0) {\n                    // Get the most recently active session\n                    const mostRecentSession = activeSessions.reduce((latest, current)=>current[1].lastModified > latest[1].lastModified ? current : latest);\n                    currentSessionId = mostRecentSession[0];\n                    console.log(\"\\uD83D\\uDD04 Restored most recent active session: \".concat(currentSessionId));\n                }\n            }\n            if (sessionsData) {\n                const parsedSessions = JSON.parse(sessionsData);\n                this.sessions = new Map(Object.entries(parsedSessions));\n            }\n            this.currentSessionId = currentSessionId;\n            // Store current session in sessionStorage for page refresh persistence\n            if (currentSessionId) {\n                sessionStorage.setItem(CURRENT_SESSION_KEY, currentSessionId);\n            }\n            console.log(\"\\uD83D\\uDCC2 Loaded \".concat(this.sessions.size, \" shared sessions for window \").concat(this.windowId, \", current: \").concat(currentSessionId));\n        } catch (error) {\n            console.error('Failed to load sessions from storage:', error);\n        }\n    }\n    async loadSessionsFromBackend() {\n        try {\n            // Check if user is authenticated before making API calls\n            const token =  true ? localStorage.getItem('plutoAuthToken') : 0;\n            if (!token || token.length < 10) {\n                console.log('⚠️ Invalid or missing auth token, skipping backend session loading');\n                this.useBackend = false; // Disable backend mode\n                this.loadSessionsFromStorage();\n                return;\n            }\n            console.log('🔄 Loading sessions from backend...');\n            const { sessionApi } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\"));\n            const response = await sessionApi.getAllSessions(true);\n            console.log('✅ Backend response received:', response);\n            if (response && response.sessions) {\n                // Convert backend sessions to our internal format\n                this.sessions.clear();\n                response.sessions.forEach((session)=>{\n                    const sessionData = {\n                        id: session.session_uuid,\n                        name: session.name,\n                        config: JSON.parse(session.config_snapshot || '{}'),\n                        createdAt: new Date(session.created_at).getTime(),\n                        lastModified: new Date(session.last_modified).getTime(),\n                        isActive: session.is_active,\n                        runtime: session.runtime || 0,\n                        targetPriceRows: session.target_price_rows ? JSON.parse(session.target_price_rows) : [],\n                        orderHistory: session.order_history ? JSON.parse(session.order_history) : [],\n                        currentMarketPrice: session.current_market_price || 100000,\n                        crypto1Balance: session.crypto1_balance || 10000,\n                        crypto2Balance: session.crypto2_balance || 10000,\n                        stablecoinBalance: session.stablecoin_balance || 10000,\n                        alarmSettings: session.alarm_settings ? JSON.parse(session.alarm_settings) : undefined\n                    };\n                    this.sessions.set(session.session_uuid, sessionData);\n                });\n                // Find active session\n                const activeSession = response.sessions.find((s)=>s.is_active);\n                if (activeSession) {\n                    this.currentSessionId = activeSession.session_uuid;\n                }\n                console.log(\"\\uD83D\\uDCC2 Loaded \".concat(this.sessions.size, \" sessions from backend\"));\n            }\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : String(error);\n            // Handle authentication errors gracefully\n            if (errorMessage.includes('Authentication') || errorMessage.includes('401') || errorMessage.includes('422')) {\n                console.log('🔐 Authentication issue detected, disabling backend mode');\n                this.useBackend = false; // Disable backend mode to prevent future API calls\n            } else if (errorMessage.includes('Cannot connect to server')) {\n                console.log('🌐 Backend server not available, using local storage only');\n            } else {\n                // Only log detailed errors for unexpected issues\n                console.warn('⚠️ Backend session loading failed, falling back to local storage:', errorMessage);\n            }\n            // Fallback to localStorage\n            this.loadSessionsFromStorage();\n        }\n    }\n    saveSessionsToStorage() {\n        try {\n            if (false) {}\n            // Save sessions to shared storage (all windows see same sessions)\n            const sessionsObject = Object.fromEntries(this.sessions);\n            localStorage.setItem(SESSIONS_STORAGE_KEY, JSON.stringify(sessionsObject));\n            // Save current session to window-specific storage\n            const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n            if (this.currentSessionId) {\n                localStorage.setItem(currentSessionKey, this.currentSessionId);\n            }\n        } catch (error) {\n            console.error('Failed to save sessions to storage:', error);\n        }\n    }\n    async createNewSessionWithAutoName(config, customName, currentBalances) {\n        const sessionName = customName || this.generateSessionName(config);\n        return this.createNewSession(sessionName, config, currentBalances);\n    }\n    async createNewSession(name, config, currentBalances) {\n        // Use provided balances or default values\n        const balances = currentBalances || {\n            crypto1: 10,\n            crypto2: 100000,\n            stablecoin: 0\n        };\n        if (this.useBackend) {\n            try {\n                const sessionData = {\n                    name,\n                    config: config,\n                    targetPriceRows: [],\n                    currentMarketPrice: 100000,\n                    crypto1Balance: balances.crypto1,\n                    crypto2Balance: balances.crypto2,\n                    stablecoinBalance: balances.stablecoin\n                };\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_0__.sessionApi.createSession(sessionData);\n                // Add the session to our local cache\n                const newSession = {\n                    id: response.session.session_uuid,\n                    name: response.session.name,\n                    config,\n                    createdAt: new Date(response.session.created_at).getTime(),\n                    lastModified: new Date(response.session.last_modified).getTime(),\n                    isActive: response.session.is_active,\n                    runtime: response.session.runtime || 0,\n                    targetPriceRows: [],\n                    orderHistory: [],\n                    currentMarketPrice: 100000,\n                    crypto1Balance: balances.crypto1,\n                    crypto2Balance: balances.crypto2,\n                    stablecoinBalance: balances.stablecoin\n                };\n                this.sessions.set(response.session.session_uuid, newSession);\n                console.log('✅ Session created on backend:', response.session.session_uuid);\n                return response.session.session_uuid;\n            } catch (error) {\n                console.error('❌ Failed to create session on backend, falling back to localStorage:', error);\n                this.useBackend = false;\n            }\n        }\n        // Fallback to localStorage\n        const sessionId = (0,uuid__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n        const now = Date.now();\n        const newSession = {\n            id: sessionId,\n            name,\n            config,\n            targetPriceRows: [],\n            orderHistory: [],\n            currentMarketPrice: 0,\n            crypto1Balance: balances.crypto1,\n            crypto2Balance: balances.crypto2,\n            stablecoinBalance: balances.stablecoin,\n            createdAt: now,\n            lastModified: now,\n            isActive: false,\n            runtime: 0\n        };\n        this.sessions.set(sessionId, newSession);\n        this.saveSessionsToStorage();\n        return sessionId;\n    }\n    async saveSession(sessionId, config, targetPriceRows, orderHistory, currentMarketPrice, crypto1Balance, crypto2Balance, stablecoinBalance) {\n        let isActive = arguments.length > 8 && arguments[8] !== void 0 ? arguments[8] : false, overrideRuntime = arguments.length > 9 ? arguments[9] : void 0// Optional parameter to set specific runtime\n        ;\n        try {\n            const session = this.sessions.get(sessionId);\n            if (!session) {\n                console.error('Session not found:', sessionId);\n                return false;\n            }\n            // Update runtime - use override if provided, otherwise calculate normally\n            let currentRuntime;\n            if (overrideRuntime !== undefined) {\n                // Use the provided runtime (for saved sessions)\n                currentRuntime = overrideRuntime;\n                console.log(\"\\uD83D\\uDCCA Using override runtime: \".concat(currentRuntime, \"ms for session \").concat(sessionId));\n            } else {\n                // Calculate runtime normally for active sessions\n                currentRuntime = session.runtime || 0;\n                const startTime = this.sessionStartTimes.get(sessionId);\n                if (startTime && isActive) {\n                    // Session is running, update runtime\n                    currentRuntime = (session.runtime || 0) + (Date.now() - startTime);\n                    // Reset start time for next interval\n                    this.sessionStartTimes.set(sessionId, Date.now());\n                } else if (!isActive && startTime) {\n                    // Session stopped, finalize runtime\n                    currentRuntime = (session.runtime || 0) + (Date.now() - startTime);\n                    this.sessionStartTimes.delete(sessionId);\n                } else if (isActive && !startTime) {\n                    // Session just started, record start time\n                    this.sessionStartTimes.set(sessionId, Date.now());\n                }\n            }\n            const updatedSession = {\n                ...session,\n                config,\n                targetPriceRows: [\n                    ...targetPriceRows\n                ],\n                orderHistory: [\n                    ...orderHistory\n                ],\n                currentMarketPrice,\n                crypto1Balance,\n                crypto2Balance,\n                stablecoinBalance,\n                isActive,\n                lastModified: Date.now(),\n                runtime: currentRuntime\n            };\n            this.sessions.set(sessionId, updatedSession);\n            // Save to backend only if explicitly authenticated\n            if (this.useBackend && \"object\" !== 'undefined') {\n                const token = localStorage.getItem('plutoAuthToken');\n                if (token && token.length > 10) {\n                    try {\n                        // Double-check authentication before making API call\n                        const { sessionApi } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\"));\n                        const sessionData = {\n                            name: updatedSession.name,\n                            config: config,\n                            targetPriceRows: targetPriceRows,\n                            currentMarketPrice: currentMarketPrice,\n                            crypto1Balance: crypto1Balance,\n                            crypto2Balance: crypto2Balance,\n                            stablecoinBalance: stablecoinBalance,\n                            isActive: isActive,\n                            additionalRuntime: currentRuntime\n                        };\n                        await sessionApi.updateSession(sessionId, sessionData);\n                        console.log('✅ Session saved to backend:', sessionId);\n                    } catch (error) {\n                        const errorMessage = error instanceof Error ? error.message : String(error);\n                        console.warn('❌ Backend session save failed:', errorMessage);\n                        // Disable backend mode on any authentication-related error\n                        if (errorMessage.includes('401') || errorMessage.includes('422') || errorMessage.includes('Authentication') || errorMessage.includes('required')) {\n                            console.log('🔐 Disabling backend mode due to authentication issue');\n                            this.useBackend = false;\n                        }\n                    // Continue with localStorage save as fallback\n                    }\n                } else {\n                    console.log('⚠️ Invalid or missing auth token, skipping backend session save');\n                    this.useBackend = false; // Disable backend mode if no valid token\n                }\n            }\n            this.saveSessionsToStorage();\n            return true;\n        } catch (error) {\n            console.error('Failed to save session:', error);\n            return false;\n        }\n    }\n    loadSession(sessionId) {\n        return this.sessions.get(sessionId) || null;\n    }\n    async deleteSession(sessionId) {\n        // Delete from backend first if available and user is authenticated\n        if (this.useBackend && \"object\" !== 'undefined') {\n            const token = localStorage.getItem('plutoAuthToken');\n            if (token) {\n                try {\n                    await _lib_api__WEBPACK_IMPORTED_MODULE_0__.sessionApi.deleteSession(sessionId);\n                    console.log('✅ Session deleted from backend:', sessionId);\n                } catch (error) {\n                    const errorMessage = error instanceof Error ? error.message : String(error);\n                    if (errorMessage.includes('401') || errorMessage.includes('422') || errorMessage.includes('Authentication')) {\n                        console.log('🔐 Authentication issue during session deletion, proceeding with local deletion');\n                    } else {\n                        console.error('❌ Failed to delete session from backend:', error);\n                    }\n                // Continue with local deletion as fallback\n                }\n            } else {\n                console.log('⚠️ No auth token, skipping backend session deletion');\n            }\n        }\n        const deleted = this.sessions.delete(sessionId);\n        if (deleted) {\n            if (this.currentSessionId === sessionId) {\n                this.currentSessionId = null;\n                const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n                localStorage.removeItem(currentSessionKey);\n            }\n            this.saveSessionsToStorage();\n        }\n        return deleted;\n    }\n    getAllSessions() {\n        return Array.from(this.sessions.values()).map((session)=>{\n            // Check if this session is active in ANY window by checking all window-specific storage\n            let isActiveInAnyWindow = session.isActive;\n            // Also check if this session is set as current in any window\n            if (true) {\n                try {\n                    // Check all possible window-specific current session keys\n                    for(let i = 0; i < localStorage.length; i++){\n                        const key = localStorage.key(i);\n                        if (key && key.startsWith('pluto_current_session_window_')) {\n                            const windowSessionId = localStorage.getItem(key);\n                            if (windowSessionId === session.id) {\n                                isActiveInAnyWindow = true;\n                                break;\n                            }\n                        }\n                    }\n                } catch (error) {\n                    console.warn('Error checking cross-window session activity:', error);\n                }\n            }\n            return {\n                id: session.id,\n                name: session.name,\n                pair: \"\".concat(session.config.crypto1, \"/\").concat(session.config.crypto2),\n                createdAt: session.createdAt,\n                lastModified: session.lastModified,\n                isActive: isActiveInAnyWindow,\n                runtime: this.getCurrentRuntime(session.id),\n                totalTrades: session.orderHistory.length,\n                totalProfitLoss: session.orderHistory.filter((trade)=>trade.orderType === 'SELL' && trade.realizedProfitLossCrypto2 !== undefined).reduce((sum, trade)=>sum + (trade.realizedProfitLossCrypto2 || 0), 0)\n            };\n        });\n    }\n    setCurrentSession(sessionId) {\n        if (this.sessions.has(sessionId)) {\n            this.currentSessionId = sessionId;\n            // Store in both localStorage (window-specific) and sessionStorage (page refresh persistence)\n            const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n            localStorage.setItem(currentSessionKey, sessionId);\n            sessionStorage.setItem(CURRENT_SESSION_KEY, sessionId);\n            // Mark this session as active when it becomes the current session\n            const session = this.sessions.get(sessionId);\n            if (session && !session.isActive) {\n                session.isActive = true;\n                session.lastModified = Date.now();\n                this.sessions.set(sessionId, session);\n                this.saveSessionsToStorage();\n                console.log(\"✅ Session \".concat(sessionId, \" marked as active for window \").concat(this.windowId));\n            }\n        }\n    }\n    getCurrentSessionId() {\n        return this.currentSessionId;\n    }\n    clearCurrentSession() {\n        // Mark current session as inactive before clearing\n        if (this.currentSessionId) {\n            const session = this.sessions.get(this.currentSessionId);\n            if (session && session.isActive) {\n                session.isActive = false;\n                session.lastModified = Date.now();\n                this.sessions.set(this.currentSessionId, session);\n                this.saveSessionsToStorage();\n                console.log(\"⏹️ Session \".concat(this.currentSessionId, \" marked as inactive for window \").concat(this.windowId));\n            }\n        }\n        this.currentSessionId = null;\n        if (true) {\n            const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n            localStorage.removeItem(currentSessionKey);\n            sessionStorage.removeItem(CURRENT_SESSION_KEY);\n        }\n        console.log(\"\\uD83D\\uDDD1️ Cleared current session for window \".concat(this.windowId));\n    }\n    startSessionRuntime(sessionId) {\n        this.sessionStartTimes.set(sessionId, Date.now());\n    }\n    stopSessionRuntime(sessionId) {\n        const startTime = this.sessionStartTimes.get(sessionId);\n        if (startTime) {\n            const session = this.sessions.get(sessionId);\n            if (session) {\n                const additionalRuntime = Date.now() - startTime;\n                session.runtime = (session.runtime || 0) + additionalRuntime;\n                session.lastModified = Date.now();\n                // Keep session active even when runtime stops - only deactivate on manual save or session clear\n                this.sessions.set(sessionId, session);\n                this.saveSessionsToStorage();\n            }\n            this.sessionStartTimes.delete(sessionId);\n        }\n    }\n    deactivateSession(sessionId) {\n        const session = this.sessions.get(sessionId);\n        if (session && session.isActive) {\n            session.isActive = false;\n            session.lastModified = Date.now();\n            this.sessions.set(sessionId, session);\n            this.saveSessionsToStorage();\n            console.log(\"⏹️ Session \".concat(sessionId, \" deactivated\"));\n        }\n    }\n    getCurrentRuntime(sessionId) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return 0;\n        const startTime = this.sessionStartTimes.get(sessionId);\n        if (startTime) {\n            // Session is currently running, add current runtime to stored runtime\n            return (session.runtime || 0) + (Date.now() - startTime);\n        }\n        // Session is not running, return stored runtime\n        return session.runtime || 0;\n    }\n    // Method to refresh backend connection after login/logout\n    async refreshBackendConnection() {\n        console.log('🔄 Refreshing backend connection...');\n        await this.initializeBackendConnection();\n    }\n    // Method to disable backend mode due to authentication issues\n    disableBackendMode() {\n        console.log('🔐 Disabling backend mode due to authentication issues');\n        this.useBackend = false;\n        this.isInitializing = false;\n    }\n    // Method to handle logout - switch to localStorage mode\n    handleLogout() {\n        console.log('👋 User logged out, switching to localStorage mode');\n        this.useBackend = false;\n        this.sessions.clear();\n        this.currentSessionId = null;\n        this.loadSessionsFromStorage();\n    }\n    exportSessionToJSON(sessionId) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return null;\n        return JSON.stringify(session, null, 2);\n    }\n    importSessionFromJSON(jsonData) {\n        try {\n            const sessionData = JSON.parse(jsonData);\n            const sessionId = (0,uuid__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n            const importedSession = {\n                ...sessionData,\n                id: sessionId,\n                isActive: false,\n                lastModified: Date.now()\n            };\n            this.sessions.set(sessionId, importedSession);\n            this.saveSessionsToStorage();\n            return sessionId;\n        } catch (error) {\n            console.error('Failed to import session:', error);\n            return null;\n        }\n    }\n    renameSession(sessionId, newName) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return false;\n        session.name = newName;\n        session.lastModified = Date.now();\n        this.sessions.set(sessionId, session);\n        this.saveSessionsToStorage();\n        return true;\n    }\n    async updateSessionAlarmSettings(sessionId, alarmSettings) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return false;\n        session.alarmSettings = alarmSettings;\n        session.lastModified = Date.now();\n        this.sessions.set(sessionId, session);\n        // Save to backend if available\n        if (this.useBackend) {\n            try {\n                const sessionData = {\n                    name: session.name,\n                    config: session.config,\n                    targetPriceRows: session.targetPriceRows,\n                    currentMarketPrice: session.currentMarketPrice,\n                    crypto1Balance: session.crypto1Balance,\n                    crypto2Balance: session.crypto2Balance,\n                    stablecoinBalance: session.stablecoinBalance,\n                    isActive: session.isActive,\n                    alarm_settings: alarmSettings\n                };\n                await _lib_api__WEBPACK_IMPORTED_MODULE_0__.sessionApi.updateSession(sessionId, sessionData);\n                console.log('✅ Session alarm settings saved to backend:', sessionId);\n            } catch (error) {\n                console.error('❌ Failed to save session alarm settings to backend:', error);\n            // Continue with localStorage save as fallback\n            }\n        }\n        this.saveSessionsToStorage();\n        return true;\n    }\n    getSessionHistory(sessionId) {\n        const session = this.sessions.get(sessionId);\n        return session ? [\n            ...session.orderHistory\n        ] : [];\n    }\n    exportSessionToCSV(sessionId) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return null;\n        const headers = [\n            'Date',\n            'Time',\n            'Pair',\n            'Crypto',\n            'Order Type',\n            'Amount',\n            'Avg Price',\n            'Value',\n            'Price 1',\n            'Crypto 1',\n            'Price 2',\n            'Crypto 2',\n            'Profit/Loss (Crypto1)',\n            'Profit/Loss (Crypto2)'\n        ];\n        const csvContent = [\n            headers.join(','),\n            ...session.orderHistory.map((entry)=>{\n                var _entry_amountCrypto1, _entry_avgPrice, _entry_valueCrypto2, _entry_price1, _entry_price2, _entry_realizedProfitLossCrypto1, _entry_realizedProfitLossCrypto2;\n                return [\n                    new Date(entry.timestamp).toISOString().split('T')[0],\n                    new Date(entry.timestamp).toTimeString().split(' ')[0],\n                    entry.pair,\n                    entry.crypto1Symbol,\n                    entry.orderType,\n                    ((_entry_amountCrypto1 = entry.amountCrypto1) === null || _entry_amountCrypto1 === void 0 ? void 0 : _entry_amountCrypto1.toFixed(session.config.numDigits)) || '',\n                    ((_entry_avgPrice = entry.avgPrice) === null || _entry_avgPrice === void 0 ? void 0 : _entry_avgPrice.toFixed(session.config.numDigits)) || '',\n                    ((_entry_valueCrypto2 = entry.valueCrypto2) === null || _entry_valueCrypto2 === void 0 ? void 0 : _entry_valueCrypto2.toFixed(session.config.numDigits)) || '',\n                    ((_entry_price1 = entry.price1) === null || _entry_price1 === void 0 ? void 0 : _entry_price1.toFixed(session.config.numDigits)) || '',\n                    entry.crypto1Symbol,\n                    ((_entry_price2 = entry.price2) === null || _entry_price2 === void 0 ? void 0 : _entry_price2.toFixed(session.config.numDigits)) || '',\n                    entry.crypto2Symbol,\n                    ((_entry_realizedProfitLossCrypto1 = entry.realizedProfitLossCrypto1) === null || _entry_realizedProfitLossCrypto1 === void 0 ? void 0 : _entry_realizedProfitLossCrypto1.toFixed(session.config.numDigits)) || '',\n                    ((_entry_realizedProfitLossCrypto2 = entry.realizedProfitLossCrypto2) === null || _entry_realizedProfitLossCrypto2 === void 0 ? void 0 : _entry_realizedProfitLossCrypto2.toFixed(session.config.numDigits)) || ''\n                ].join(',');\n            })\n        ].join('\\n');\n        return csvContent;\n    }\n    clearAllSessions() {\n        this.sessions.clear();\n        this.currentSessionId = null;\n        // Clear shared sessions storage\n        localStorage.removeItem(SESSIONS_STORAGE_KEY);\n        // Clear window-specific current session\n        const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n        localStorage.removeItem(currentSessionKey);\n    }\n    // Auto-save functionality\n    enableAutoSave(sessionId, getSessionData) {\n        let intervalMs = arguments.length > 2 && arguments[2] !== void 0 // 30 seconds\n         ? arguments[2] : 30000;\n        const interval = setInterval(()=>{\n            const data = getSessionData();\n            this.saveSession(sessionId, data.config, data.targetPriceRows, data.orderHistory, data.currentMarketPrice, data.crypto1Balance, data.crypto2Balance, data.stablecoinBalance, data.isActive);\n        }, intervalMs);\n        return ()=>clearInterval(interval);\n    }\n    constructor(){\n        this.sessions = new Map();\n        this.currentSessionId = null;\n        this.useBackend = true // Flag to determine if we should use backend API\n        ;\n        this.isInitializing = false // Prevent multiple initialization attempts\n        ;\n        this.sessionStartTimes = new Map() // Track when sessions started running\n        ;\n        this.windowId = getWindowId();\n        console.log(\"\\uD83E\\uDE9F SessionManager initialized for window: \".concat(this.windowId));\n        // Clear any stale session start times on initialization\n        this.sessionStartTimes.clear();\n        // Enable backend by default - we want persistent sessions\n        this.useBackend = true;\n        // Load sessions and setup cross-window sync\n        this.loadSessionsFromStorage();\n        this.setupStorageListener();\n        // Check backend connection and load sessions from backend (with delay to ensure auth is ready)\n        setTimeout(()=>{\n            this.initializeBackendConnection();\n        }, 1000); // 1 second delay to allow auth to be properly initialized\n        console.log(\"\\uD83E\\uDE9F SessionManager initialized for window \".concat(this.windowId));\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/session-manager.ts\n"));

/***/ })

});