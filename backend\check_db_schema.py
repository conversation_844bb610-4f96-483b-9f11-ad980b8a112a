#!/usr/bin/env python3
"""Check database schema."""

import sqlite3
import os

def check_schema():
    """Check the current database schema."""
    
    db_path = 'pluto.db'
    if not os.path.exists(db_path):
        print(f"❌ Database file {db_path} not found")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check what tables exist
        print("=== All tables in database ===")
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()

        for table in tables:
            print(f"Table: {table[0]}")

        if not tables:
            print("❌ No tables found in database")
            return

        # Check trading_sessions table schema if it exists
        table_names = [table[0] for table in tables]
        if 'trading_sessions' in table_names:
            print("\n=== trading_sessions table schema ===")
            cursor.execute("PRAGMA table_info(trading_sessions)")
            columns = cursor.fetchall()

            for col in columns:
                print(f"{col[1]} - {col[2]} - {'NOT NULL' if col[3] else 'NULL'}")

            print(f"\nTotal columns: {len(columns)}")

            # Check if alarm_settings exists
            column_names = [col[1] for col in columns]
            if 'alarm_settings' in column_names:
                print("✅ alarm_settings column exists")
            else:
                print("❌ alarm_settings column missing")
        else:
            print("❌ trading_sessions table does not exist")
            
    except Exception as e:
        print(f"❌ Error checking schema: {str(e)}")
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    check_schema()
