
"use client";
import type { ReactNode } from 'react';
import React, { createContext, useContext, useState, useEffect } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { Loader2 } from 'lucide-react';
import { authApi } from '@/lib/api';

interface AuthContextType {
  isAuthenticated: boolean;
  login: (username: string, password: string) => Promise<boolean>;
  logout: () => void;
  isLoading: boolean;
}
const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();
  const pathname = usePathname();
  
  useEffect(() => {
    // Check auth status from localStorage
    const storedAuthStatus = localStorage.getItem('plutoAuth');
    const authToken = localStorage.getItem('plutoAuthToken');
    if (storedAuthStatus === 'true' && authToken) {
      setIsAuthenticated(true);
    }
    setIsLoading(false);
  }, []);
  
  useEffect(() => {
    if (!isLoading && !isAuthenticated && pathname !== '/login') {
      router.push('/login');
    } else if (!isLoading && isAuthenticated && pathname === '/login') {
      router.push('/dashboard');
    }
  }, [isAuthenticated, isLoading, pathname, router]);
  
  const login = async (username: string, password: string): Promise<boolean> => {
    setIsLoading(true);
    try {
      const success = await authApi.login(username, password);
      if (success) {
        setIsAuthenticated(true);

        // Refresh SessionManager backend connection after successful login
        try {
          const { SessionManager } = await import('@/lib/session-manager');
          await SessionManager.getInstance().refreshBackendConnection();
        } catch (error) {
          console.error('Failed to refresh session manager:', error);
        }

        router.push('/dashboard');
        return true;
      }
      setIsAuthenticated(false);
      return false;
    } catch (error) {
      console.error('Login failed:', error);
      setIsAuthenticated(false);
      return false;
    } finally {
      setIsLoading(false);
    }
  };
  
  const logout = async () => {
    try {
      await authApi.logout();
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      // Handle logout in SessionManager
      try {
        const { SessionManager } = await import('@/lib/session-manager');
        SessionManager.getInstance().handleLogout();
      } catch (error) {
        console.error('Failed to handle session manager logout:', error);
      }

      setIsAuthenticated(false);
      router.push('/login');
    }
  };

  // Consistent loading display across contexts
  if (isLoading && !pathname?.startsWith('/_next/static/')) {
    return (
      <div className="flex items-center justify-center h-screen bg-background text-foreground">
        <Loader2 className="h-12 w-12 animate-spin text-primary" />
        <p className="ml-4 text-xl">Loading Pluto...</p>
      </div>
    );
  }
  
  if (!isAuthenticated && pathname !== '/login' && !pathname?.startsWith('/_next/static/')) {
    return (
      <div className="flex items-center justify-center h-screen bg-background text-foreground">
        <Loader2 className="h-12 w-12 animate-spin text-primary" />
        <p className="ml-4 text-xl">Redirecting to login...</p>
      </div>
    );
  }

  return (
    <AuthContext.Provider value={{ isAuthenticated, login, logout, isLoading }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
