#!/usr/bin/env python3
"""Force create database with all tables."""

import os
import sys

# Add the backend directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from db import db, register_models

def force_create_database():
    """Force create the database with all tables."""
    
    # Remove existing database
    db_path = 'pluto.db'
    if os.path.exists(db_path):
        os.remove(db_path)
        print(f"Removed existing database: {db_path}")
    
    # Create app and database
    app = create_app()
    
    with app.app_context():
        print("Registering models...")
        models = register_models()
        print(f"Registered models: {list(models.keys())}")
        
        print("Creating all tables...")
        db.create_all()
        
        # Verify tables were created
        from sqlalchemy import inspect
        inspector = inspect(db.engine)
        tables = inspector.get_table_names()
        
        print(f"Created tables: {tables}")
        
        if 'trading_sessions' in tables:
            print("✅ trading_sessions table created")
            
            # Check columns
            columns = inspector.get_columns('trading_sessions')
            column_names = [col['name'] for col in columns]
            print(f"Columns: {column_names}")
            
            if 'alarm_settings' in column_names:
                print("✅ alarm_settings column exists")
            else:
                print("❌ alarm_settings column missing")
        else:
            print("❌ trading_sessions table not created")
        
        print("Database creation completed!")

if __name__ == "__main__":
    force_create_database()
