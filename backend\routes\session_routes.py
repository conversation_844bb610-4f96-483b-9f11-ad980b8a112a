from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from db import db
from models.trading_session_model import TradingSession
from models.trade_model import Trade
import logging
import uuid
from datetime import datetime
import json

logger = logging.getLogger(__name__)
session_bp = Blueprint('sessions', __name__, url_prefix='/sessions')

@session_bp.route('/', methods=['GET'])
@jwt_required()
def get_user_sessions():
    """Get all sessions for the current user."""
    try:
        current_user_id = int(get_jwt_identity())
        include_inactive = request.args.get('include_inactive', 'true').lower() == 'true'

        sessions = TradingSession.get_user_sessions(current_user_id, include_inactive)
        
        return jsonify({
            "sessions": [session.to_metadata_dict() for session in sessions],
            "total": len(sessions)
        }), 200
        
    except Exception as e:
        logger.error(f"Error getting user sessions: {str(e)}")
        return jsonify({
            "error": "Failed to get sessions",
            "message": str(e)
        }), 500

@session_bp.route('/', methods=['POST'])
@jwt_required()
def create_session():
    """Create a new trading session."""
    try:
        current_user_id = int(get_jwt_identity())
        data = request.get_json()
        
        if not data:
            return jsonify({"error": "Missing JSON data"}), 400
            
        required_fields = ['name', 'config']
        for field in required_fields:
            if field not in data:
                return jsonify({"error": f"Missing required field: {field}"}), 400
        
        # Generate UUID for the session
        session_uuid = str(uuid.uuid4())
        
        # Create new session
        session = TradingSession(
            user_id=current_user_id,
            session_uuid=session_uuid,
            name=data['name'],
            config_snapshot=json.dumps(data['config']),
            description=data.get('description', '')
        )
        
        # Set initial state if provided
        if 'targetPriceRows' in data:
            session.set_target_price_rows(data['targetPriceRows'])
        if 'currentMarketPrice' in data:
            session.current_market_price = data['currentMarketPrice']
        if 'crypto1Balance' in data:
            session.crypto1_balance = data['crypto1Balance']
        if 'crypto2Balance' in data:
            session.crypto2_balance = data['crypto2Balance']
        if 'stablecoinBalance' in data:
            session.stablecoin_balance = data['stablecoinBalance']
        
        db.session.add(session)
        db.session.commit()
        
        logger.info(f"Created new session: {session.name} (UUID: {session_uuid}) for user {current_user_id}")
        
        return jsonify({
            "message": "Session created successfully",
            "session": session.to_dict()
        }), 201
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"Error creating session: {str(e)}")
        return jsonify({
            "error": "Failed to create session",
            "message": str(e)
        }), 500

@session_bp.route('/<string:session_uuid>', methods=['GET'])
@jwt_required()
def get_session(session_uuid):
    """Get a specific session by UUID."""
    try:
        current_user_id = int(get_jwt_identity())

        session = TradingSession.query.filter_by(
            session_uuid=session_uuid,
            user_id=current_user_id
        ).first()
        
        if not session:
            return jsonify({"error": "Session not found"}), 404
        
        return jsonify({
            "session": session.to_dict()
        }), 200
        
    except Exception as e:
        logger.error(f"Error getting session {session_uuid}: {str(e)}")
        return jsonify({
            "error": "Failed to get session",
            "message": str(e)
        }), 500

@session_bp.route('/<string:session_uuid>', methods=['PUT'])
@jwt_required()
def update_session(session_uuid):
    """Update a session's state."""
    try:
        current_user_id = int(get_jwt_identity())
        data = request.get_json()

        if not data:
            return jsonify({"error": "Missing JSON data"}), 400

        session = TradingSession.query.filter_by(
            session_uuid=session_uuid,
            user_id=current_user_id
        ).first()
        
        if not session:
            return jsonify({"error": "Session not found"}), 404
        
        # Update session fields
        if 'name' in data:
            session.name = data['name']
        if 'description' in data:
            session.description = data['description']
        if 'config' in data:
            session.set_config_snapshot(data['config'])
        if 'alarm_settings' in data:
            session.set_alarm_settings(data['alarm_settings'])
        
        # Update session state
        session.update_session_state(
            target_price_rows=data.get('targetPriceRows'),
            current_market_price=data.get('currentMarketPrice'),
            crypto1_balance=data.get('crypto1Balance'),
            crypto2_balance=data.get('crypto2Balance'),
            stablecoin_balance=data.get('stablecoinBalance'),
            is_active=data.get('isActive')
        )
        
        # Update runtime if provided
        if 'additionalRuntime' in data:
            session.update_runtime(data['additionalRuntime'])
        
        db.session.commit()
        
        logger.info(f"Updated session: {session.name} (UUID: {session_uuid})")
        
        return jsonify({
            "message": "Session updated successfully",
            "session": session.to_dict()
        }), 200
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"Error updating session {session_uuid}: {str(e)}")
        return jsonify({
            "error": "Failed to update session",
            "message": str(e)
        }), 500

@session_bp.route('/<string:session_uuid>', methods=['DELETE'])
@jwt_required()
def delete_session(session_uuid):
    """Delete a session."""
    try:
        current_user_id = int(get_jwt_identity())

        session = TradingSession.query.filter_by(
            session_uuid=session_uuid,
            user_id=current_user_id
        ).first()
        
        if not session:
            return jsonify({"error": "Session not found"}), 404
        
        # Don't allow deletion of active sessions
        if session.is_active:
            return jsonify({"error": "Cannot delete active session"}), 400
        
        session_name = session.name
        db.session.delete(session)
        db.session.commit()
        
        logger.info(f"Deleted session: {session_name} (UUID: {session_uuid})")
        
        return jsonify({
            "message": "Session deleted successfully"
        }), 200
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"Error deleting session {session_uuid}: {str(e)}")
        return jsonify({
            "error": "Failed to delete session",
            "message": str(e)
        }), 500

@session_bp.route('/<string:session_uuid>/activate', methods=['POST'])
@jwt_required()
def activate_session(session_uuid):
    """Activate a session (deactivates all other sessions for the user)."""
    try:
        current_user_id = int(get_jwt_identity())

        session = TradingSession.query.filter_by(
            session_uuid=session_uuid,
            user_id=current_user_id
        ).first()
        
        if not session:
            return jsonify({"error": "Session not found"}), 404
        
        # Deactivate all other sessions for this user
        TradingSession.deactivate_all_user_sessions(current_user_id)
        
        # Activate this session
        session.is_active = True
        session.last_active_at = datetime.utcnow()
        session.last_modified = datetime.utcnow()
        
        db.session.commit()
        
        logger.info(f"Activated session: {session.name} (UUID: {session_uuid})")
        
        return jsonify({
            "message": "Session activated successfully",
            "session": session.to_dict()
        }), 200
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"Error activating session {session_uuid}: {str(e)}")
        return jsonify({
            "error": "Failed to activate session",
            "message": str(e)
        }), 500

@session_bp.route('/<string:session_uuid>/history', methods=['GET'])
@jwt_required()
def get_session_history(session_uuid):
    """Get trade history for a specific session."""
    try:
        current_user_id = int(get_jwt_identity())

        session = TradingSession.query.filter_by(
            session_uuid=session_uuid,
            user_id=current_user_id
        ).first()
        
        if not session:
            return jsonify({"error": "Session not found"}), 404
        
        # Get trades for this session
        trades = Trade.query.filter_by(
            session_id=session.id,
            user_id=current_user_id
        ).order_by(Trade.timestamp.desc()).all()
        
        return jsonify({
            "trades": [trade.to_dict() for trade in trades],
            "total": len(trades),
            "session": session.to_metadata_dict()
        }), 200
        
    except Exception as e:
        logger.error(f"Error getting session history {session_uuid}: {str(e)}")
        return jsonify({
            "error": "Failed to get session history",
            "message": str(e)
        }), 500

@session_bp.route('/active', methods=['GET'])
@jwt_required()
def get_active_session():
    """Get the currently active session for the user."""
    try:
        current_user_id = int(get_jwt_identity())

        session = TradingSession.get_active_session(current_user_id)
        
        if not session:
            return jsonify({"session": None}), 200
        
        return jsonify({
            "session": session.to_dict()
        }), 200
        
    except Exception as e:
        logger.error(f"Error getting active session: {str(e)}")
        return jsonify({
            "error": "Failed to get active session",
            "message": str(e)
        }), 500
