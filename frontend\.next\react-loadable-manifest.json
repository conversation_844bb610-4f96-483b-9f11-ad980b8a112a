{"..\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\utils\\use-websocket.js -> @vercel/turbopack-ecmascript-runtime/browser/dev/hmr-client/hmr-client.ts": {"id": "..\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\utils\\use-websocket.js -> @vercel/turbopack-ecmascript-runtime/browser/dev/hmr-client/hmr-client.ts", "files": ["static/chunks/_app-pages-browser_node_modules_next_dist_client_dev_noop-turbopack-hmr_js.js"]}, "contexts\\AuthContext.tsx -> @/lib/session-manager": {"id": "contexts\\AuthContext.tsx -> @/lib/session-manager", "files": ["static/chunks/_app-pages-browser_src_lib_session-manager_ts.js"]}, "lib\\session-manager.ts -> @/lib/api": {"id": "lib\\session-manager.ts -> @/lib/api", "files": []}}