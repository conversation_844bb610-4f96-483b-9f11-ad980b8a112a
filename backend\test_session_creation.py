#!/usr/bin/env python3
"""Test script to verify session creation API works correctly."""

import requests
import json

# Test configuration
BASE_URL = "http://localhost:5000"
USERNAME = "testuser"
PASSWORD = "password123"

def test_session_creation():
    """Test the complete session creation flow."""
    
    # Step 1: Login to get auth token
    print("Step 1: Logging in...")
    login_data = {
        "username": USERNAME,
        "password": PASSWORD
    }
    
    login_response = requests.post(
        f"{BASE_URL}/auth/login",
        json=login_data,
        headers={"Content-Type": "application/json"}
    )
    
    if login_response.status_code != 200:
        print(f"❌ Login failed: {login_response.status_code}")
        print(login_response.text)
        return False
    
    login_result = login_response.json()
    access_token = login_result["access_token"]
    print(f"✅ Login successful, got token: {access_token[:50]}...")
    
    # Step 2: Create a test session
    print("\nStep 2: Creating test session...")
    session_data = {
        "name": "Test Session",
        "config": {
            "crypto1": "BTC",
            "crypto2": "ETH",
            "preferredStablecoin": "USDT",
            "baseBid": 100,
            "multiplier": 1.005,
            "displayDigits": 4,
            "slippagePercent": 0.2,
            "enableStablecoinSwapMode": True
        },
        "targetPriceRows": [],
        "currentMarketPrice": 100000,
        "crypto1Balance": 10,
        "crypto2Balance": 100000,
        "stablecoinBalance": 0
    }
    
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {access_token}"
    }
    
    session_response = requests.post(
        f"{BASE_URL}/sessions/",
        json=session_data,
        headers=headers
    )
    
    if session_response.status_code != 201:
        print(f"❌ Session creation failed: {session_response.status_code}")
        print(session_response.text)
        return False
    
    session_result = session_response.json()
    session_id = session_result["session"]["id"]
    print(f"✅ Session created successfully: {session_id}")
    
    # Step 3: Verify session can be retrieved
    print("\nStep 3: Retrieving created session...")
    get_response = requests.get(
        f"{BASE_URL}/sessions/{session_id}",
        headers=headers
    )
    
    if get_response.status_code != 200:
        print(f"❌ Session retrieval failed: {get_response.status_code}")
        print(get_response.text)
        return False
    
    retrieved_session = get_response.json()
    print(f"✅ Session retrieved successfully: {retrieved_session['session']['name']}")
    
    print("\n🎉 All tests passed! Session creation API is working correctly.")
    return True

if __name__ == "__main__":
    test_session_creation()
