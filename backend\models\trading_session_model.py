from db import db
from datetime import datetime
import json
from sqlalchemy.orm import relationship

class TradingSession(db.Model):
    """Model for managing trading sessions - video game style save/load functionality."""
    __tablename__ = 'trading_sessions'
    __table_args__ = {'extend_existing': True}

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.<PERSON>ey('users.id'), nullable=False)
    session_uuid = db.Column(db.String(36), unique=True, nullable=False)  # UUID for frontend
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text, nullable=True)
    
    # Trading configuration snapshot
    config_snapshot = db.Column(db.Text, nullable=False)  # JSON of trading config
    
    # Session state
    target_price_rows = db.Column(db.Text, nullable=True)  # JSON array of target price states
    current_market_price = db.Column(db.Float, nullable=True)
    crypto1_balance = db.Column(db.Float, default=10.0)
    crypto2_balance = db.Column(db.Float, default=100000.0)
    stablecoin_balance = db.Column(db.Float, default=0.0)
    alarm_settings = db.Column(db.Text, nullable=True)  # JSON of session-specific alarm settings
    
    # Session metadata
    is_active = db.Column(db.Boolean, default=False)
    runtime_seconds = db.Column(db.Integer, default=0)  # Total runtime in seconds
    total_trades = db.Column(db.Integer, default=0)
    total_profit_loss_crypto1 = db.Column(db.Float, default=0.0)
    total_profit_loss_crypto2 = db.Column(db.Float, default=0.0)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_modified = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    last_active_at = db.Column(db.DateTime, nullable=True)
    
    # Relationships
    user = relationship("User")
    trades = relationship("Trade", back_populates="trading_session", cascade="all, delete-orphan")
    
    def __init__(self, user_id, session_uuid, name, config_snapshot, description=None):
        self.user_id = user_id
        self.session_uuid = session_uuid
        self.name = name
        self.description = description
        self.config_snapshot = config_snapshot
        self.target_price_rows = json.dumps([])
        self.is_active = False
        self.runtime_seconds = 0
        self.total_trades = 0
        self.total_profit_loss_crypto1 = 0.0
        self.total_profit_loss_crypto2 = 0.0

    def get_config_snapshot(self):
        """Convert stored JSON string to Python dict."""
        if not self.config_snapshot:
            return {}
        try:
            return json.loads(self.config_snapshot)
        except json.JSONDecodeError:
            return {}

    def set_config_snapshot(self, config_dict):
        """Convert Python dict to JSON string for storage."""
        self.config_snapshot = json.dumps(config_dict)

    def get_target_price_rows(self):
        """Convert stored JSON string to Python list."""
        if not self.target_price_rows:
            return []
        try:
            return json.loads(self.target_price_rows)
        except json.JSONDecodeError:
            return []

    def set_target_price_rows(self, rows):
        """Convert Python list to JSON string for storage."""
        self.target_price_rows = json.dumps(rows)

    def get_alarm_settings(self):
        """Convert stored JSON string to Python dict."""
        if not self.alarm_settings:
            return None
        try:
            return json.loads(self.alarm_settings)
        except json.JSONDecodeError:
            return None

    def set_alarm_settings(self, settings_dict):
        """Convert Python dict to JSON string for storage."""
        if settings_dict is None:
            self.alarm_settings = None
        else:
            self.alarm_settings = json.dumps(settings_dict)

    def update_session_state(self, target_price_rows=None, current_market_price=None, 
                           crypto1_balance=None, crypto2_balance=None, stablecoin_balance=None,
                           is_active=None):
        """Update session state with new data."""
        if target_price_rows is not None:
            self.set_target_price_rows(target_price_rows)
        if current_market_price is not None:
            self.current_market_price = current_market_price
        if crypto1_balance is not None:
            self.crypto1_balance = crypto1_balance
        if crypto2_balance is not None:
            self.crypto2_balance = crypto2_balance
        if stablecoin_balance is not None:
            self.stablecoin_balance = stablecoin_balance
        if is_active is not None:
            self.is_active = is_active
            if is_active:
                self.last_active_at = datetime.utcnow()
        
        self.last_modified = datetime.utcnow()

    def add_trade_to_session(self, profit_loss_crypto1=0.0, profit_loss_crypto2=0.0):
        """Update session statistics when a trade is completed."""
        self.total_trades += 1
        self.total_profit_loss_crypto1 += profit_loss_crypto1
        self.total_profit_loss_crypto2 += profit_loss_crypto2
        self.last_modified = datetime.utcnow()

    def update_runtime(self, additional_seconds):
        """Add runtime to the session."""
        self.runtime_seconds += additional_seconds
        self.last_modified = datetime.utcnow()

    def to_dict(self):
        """Convert session to dictionary for API responses."""
        return {
            'id': self.session_uuid,  # Use UUID for frontend
            'name': self.name,
            'description': self.description,
            'config': self.get_config_snapshot(),
            'targetPriceRows': self.get_target_price_rows(),
            'currentMarketPrice': self.current_market_price,
            'crypto1Balance': self.crypto1_balance,
            'crypto2Balance': self.crypto2_balance,
            'stablecoinBalance': self.stablecoin_balance,
            'isActive': self.is_active,
            'runtime': self.runtime_seconds,
            'totalTrades': self.total_trades,
            'totalProfitLossCrypto1': self.total_profit_loss_crypto1,
            'totalProfitLossCrypto2': self.total_profit_loss_crypto2,
            'alarmSettings': self.get_alarm_settings(),
            'createdAt': self.created_at.isoformat() if self.created_at else None,
            'lastModified': self.last_modified.isoformat() if self.last_modified else None,
            'lastActiveAt': self.last_active_at.isoformat() if self.last_active_at else None
        }

    def to_metadata_dict(self):
        """Convert session to metadata dictionary for session lists."""
        config = self.get_config_snapshot()
        return {
            'id': self.session_uuid,
            'name': self.name,
            'pair': f"{config.get('crypto1', 'N/A')}/{config.get('crypto2', 'N/A')}",
            'createdAt': int(self.created_at.timestamp() * 1000) if self.created_at else 0,
            'lastModified': int(self.last_modified.timestamp() * 1000) if self.last_modified else 0,
            'isActive': self.is_active,
            'runtime': self.runtime_seconds * 1000,  # Convert to milliseconds for frontend
            'totalTrades': self.total_trades,
            'totalProfitLoss': self.total_profit_loss_crypto2
        }

    @staticmethod
    def get_user_sessions(user_id, include_inactive=True):
        """Get all sessions for a user."""
        query = TradingSession.query.filter_by(user_id=user_id)
        if not include_inactive:
            query = query.filter_by(is_active=True)
        return query.order_by(TradingSession.last_modified.desc()).all()

    @staticmethod
    def get_active_session(user_id):
        """Get the currently active session for a user."""
        return TradingSession.query.filter_by(user_id=user_id, is_active=True).first()

    @staticmethod
    def deactivate_all_user_sessions(user_id):
        """Deactivate all sessions for a user (only one can be active at a time)."""
        TradingSession.query.filter_by(user_id=user_id, is_active=True).update({'is_active': False})
        db.session.commit()
