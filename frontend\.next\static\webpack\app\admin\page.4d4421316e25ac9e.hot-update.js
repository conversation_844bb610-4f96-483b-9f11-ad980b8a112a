"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./src/components/admin/SessionManager.tsx":
/*!*************************************************!*\
  !*** ./src/components/admin/SessionManager.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SessionManager: () => (/* binding */ SessionManager)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./src/components/ui/scroll-area.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,Save,Trash2,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,Save,Trash2,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,Save,Trash2,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pen.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,Save,Trash2,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/volume-2.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,Save,Trash2,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/folder-open.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,Save,Trash2,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,Save,Trash2,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,Save,Trash2,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,Save,Trash2,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _lib_session_manager__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/session-manager */ \"(app-pages-browser)/./src/lib/session-manager.ts\");\n/* harmony import */ var _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/contexts/TradingContext */ \"(app-pages-browser)/./src/contexts/TradingContext.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _components_modals_SessionAlarmConfigModal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/modals/SessionAlarmConfigModal */ \"(app-pages-browser)/./src/components/modals/SessionAlarmConfigModal.tsx\");\n/* __next_internal_client_entry_do_not_use__ SessionManager auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction SessionManager() {\n    _s();\n    const { config, targetPriceRows, orderHistory, currentMarketPrice, crypto1Balance, crypto2Balance, stablecoinBalance, botSystemStatus, dispatch } = (0,_contexts_TradingContext__WEBPACK_IMPORTED_MODULE_8__.useTradingContext)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__.useToast)();\n    const [sessions, setSessions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentSessionId, setCurrentSessionId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingSessionId, setEditingSessionId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingName, setEditingName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [currentRuntime, setCurrentRuntime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [alarmModalOpen, setAlarmModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedSessionForAlarm, setSelectedSessionForAlarm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_7__.SessionManager.getInstance();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SessionManager.useEffect\": ()=>{\n            loadSessions();\n            const sessionId = sessionManager.getCurrentSessionId();\n            setCurrentSessionId(sessionId);\n            // Initialize current runtime\n            if (sessionId) {\n                const runtime = sessionManager.getCurrentRuntime(sessionId);\n                setCurrentRuntime(runtime);\n            }\n            // Listen for storage changes to sync sessions across windows\n            const handleStorageChange = {\n                \"SessionManager.useEffect.handleStorageChange\": (event)=>{\n                    var _event_key;\n                    if ((event.key === 'pluto_trading_sessions' || ((_event_key = event.key) === null || _event_key === void 0 ? void 0 : _event_key.startsWith('pluto_current_session_window_'))) && event.newValue) {\n                        loadSessions();\n                        console.log('🔄 Sessions synced from another window');\n                    }\n                }\n            }[\"SessionManager.useEffect.handleStorageChange\"];\n            window.addEventListener('storage', handleStorageChange);\n            return ({\n                \"SessionManager.useEffect\": ()=>{\n                    window.removeEventListener('storage', handleStorageChange);\n                }\n            })[\"SessionManager.useEffect\"];\n        }\n    }[\"SessionManager.useEffect\"], []);\n    // Update runtime display every second for active sessions\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SessionManager.useEffect\": ()=>{\n            const interval = setInterval({\n                \"SessionManager.useEffect.interval\": ()=>{\n                    if (currentSessionId) {\n                        const runtime = sessionManager.getCurrentRuntime(currentSessionId);\n                        setCurrentRuntime(runtime);\n                        console.log(\"⏱️ Runtime update: \".concat(formatRuntime(runtime), \" for session \").concat(currentSessionId));\n                    }\n                }\n            }[\"SessionManager.useEffect.interval\"], 5000); // Update every 5 seconds instead of 1 second\n            return ({\n                \"SessionManager.useEffect\": ()=>clearInterval(interval)\n            })[\"SessionManager.useEffect\"];\n        }\n    }[\"SessionManager.useEffect\"], [\n        currentSessionId,\n        sessionManager\n    ]);\n    const loadSessions = ()=>{\n        const allSessions = sessionManager.getAllSessions();\n        setSessions(allSessions.sort((a, b)=>b.lastModified - a.lastModified));\n        // Also update the list of active sessions across all windows\n        const activeSessionIds = sessionManager.getAllActiveSessionsAcrossWindows();\n        console.log(\"\\uD83E\\uDE9F Active sessions across all windows: \".concat(activeSessionIds.length), activeSessionIds);\n    };\n    const handleRefreshFromBackend = async ()=>{\n        try {\n            const success = await sessionManager.forceRefreshFromBackend();\n            if (success) {\n                loadSessions();\n                toast({\n                    title: \"Sessions Refreshed\",\n                    description: \"Sessions have been refreshed from the database\"\n                });\n            } else {\n                toast({\n                    title: \"Refresh Failed\",\n                    description: \"Failed to refresh sessions from database\",\n                    variant: \"destructive\"\n                });\n            }\n        } catch (error) {\n            console.error('Error refreshing sessions:', error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to refresh sessions from database\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleSaveCurrentSession = async ()=>{\n        if (!currentSessionId) {\n            toast({\n                title: \"Error\",\n                description: \"No active session to save\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        try {\n            // Check if there's already a saved version of this session\n            const currentSession = sessionManager.loadSession(currentSessionId);\n            if (!currentSession) {\n                toast({\n                    title: \"Error\",\n                    description: \"Current session not found\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            // Look for existing saved session with same base name (only manually saved ones)\n            const allSessions = sessionManager.getAllSessions();\n            const baseName = currentSession.name.replace(/ \\((Saved|AutoSaved).*\\)$/, ''); // Remove existing timestamp\n            const existingSavedSession = allSessions.find((s)=>s.id !== currentSessionId && s.name.startsWith(baseName) && s.name.includes('(Saved') && // Only look for manually saved sessions\n                !s.isActive // Only look in inactive sessions\n            );\n            let targetSessionId;\n            let savedName;\n            if (existingSavedSession) {\n                // Update existing saved session - update the timestamp to show latest save\n                targetSessionId = existingSavedSession.id;\n                const timestamp = new Date().toLocaleString('en-US', {\n                    month: 'short',\n                    day: 'numeric',\n                    hour: '2-digit',\n                    minute: '2-digit',\n                    hour12: false\n                });\n                savedName = \"\".concat(baseName, \" (Saved \").concat(timestamp, \")\");\n                console.log(\"\\uD83D\\uDCDD Updating existing saved session: \".concat(savedName));\n            } else {\n                // Create new saved session with timestamp\n                const timestamp = new Date().toLocaleString('en-US', {\n                    month: 'short',\n                    day: 'numeric',\n                    hour: '2-digit',\n                    minute: '2-digit',\n                    hour12: false\n                });\n                savedName = \"\".concat(baseName, \" (Saved \").concat(timestamp, \")\");\n                targetSessionId = await sessionManager.createNewSession(savedName, config);\n                console.log(\"\\uD83D\\uDCBE Creating new saved session: \".concat(savedName));\n            }\n            // Get current runtime from the active session\n            const currentRuntime = sessionManager.getCurrentRuntime(currentSessionId);\n            // Update the session name if it's an existing session\n            if (existingSavedSession) {\n                sessionManager.renameSession(targetSessionId, savedName);\n            }\n            // Save/update the session with current data and runtime\n            const success = await sessionManager.saveSession(targetSessionId, config, targetPriceRows, orderHistory, currentMarketPrice, crypto1Balance, crypto2Balance, stablecoinBalance, false, currentRuntime // Pass the current runtime to the saved session\n            );\n            // DO NOT deactivate the current session - keep it running!\n            // The current session should remain active for continued trading\n            if (success) {\n                loadSessions();\n                toast({\n                    title: \"Session Saved\",\n                    description: existingSavedSession ? \"Save checkpoint updated (Runtime: \".concat(formatRuntime(currentRuntime), \")\") : \"Session saved as checkpoint (Runtime: \".concat(formatRuntime(currentRuntime), \")\")\n                });\n            } else {\n                toast({\n                    title: \"Error\",\n                    description: \"Failed to save session\",\n                    variant: \"destructive\"\n                });\n            }\n        } catch (error) {\n            console.error('Error saving session:', error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to save session\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleLoadSession = (sessionId)=>{\n        const session = sessionManager.loadSession(sessionId);\n        if (!session) {\n            toast({\n                title: \"Error\",\n                description: \"Failed to load session\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        // Load session data into context\n        dispatch({\n            type: 'SET_CONFIG',\n            payload: session.config\n        });\n        dispatch({\n            type: 'SET_TARGET_PRICE_ROWS',\n            payload: session.targetPriceRows\n        });\n        dispatch({\n            type: 'CLEAR_ORDER_HISTORY'\n        });\n        session.orderHistory.forEach((entry)=>{\n            dispatch({\n                type: 'ADD_ORDER_HISTORY_ENTRY',\n                payload: entry\n            });\n        });\n        dispatch({\n            type: 'SET_MARKET_PRICE',\n            payload: session.currentMarketPrice\n        });\n        dispatch({\n            type: 'SET_BALANCES',\n            payload: {\n                crypto1: session.crypto1Balance,\n                crypto2: session.crypto2Balance\n            }\n        });\n        sessionManager.setCurrentSession(sessionId);\n        setCurrentSessionId(sessionId);\n        loadSessions();\n        toast({\n            title: \"Session Loaded\",\n            description: 'Session \"'.concat(session.name, '\" has been loaded')\n        });\n    };\n    const handleDeleteSession = async (sessionId)=>{\n        const success = await sessionManager.deleteSession(sessionId);\n        if (success) {\n            if (currentSessionId === sessionId) {\n                setCurrentSessionId(null);\n            }\n            loadSessions();\n            toast({\n                title: \"Session Deleted\",\n                description: \"Session has been deleted successfully\"\n            });\n        }\n    };\n    const handleRenameSession = (sessionId)=>{\n        if (!editingName.trim()) return;\n        const success = sessionManager.renameSession(sessionId, editingName.trim());\n        if (success) {\n            setEditingSessionId(null);\n            setEditingName('');\n            loadSessions();\n            toast({\n                title: \"Session Renamed\",\n                description: \"Session has been renamed successfully\"\n            });\n        }\n    };\n    const handleOpenAlarmConfig = (sessionId)=>{\n        const session = sessionManager.loadSession(sessionId);\n        if (session) {\n            setSelectedSessionForAlarm({\n                id: sessionId,\n                name: session.name,\n                alarmSettings: session.alarmSettings\n            });\n            setAlarmModalOpen(true);\n        }\n    };\n    const handleSaveAlarmSettings = async (sessionId, alarmSettings)=>{\n        const success = await sessionManager.updateSessionAlarmSettings(sessionId, alarmSettings);\n        if (success) {\n            loadSessions();\n            toast({\n                title: \"Alarm Settings Saved\",\n                description: \"Session alarm settings have been updated\"\n            });\n        } else {\n            toast({\n                title: \"Error\",\n                description: \"Failed to save alarm settings\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleExportSession = (sessionId)=>{\n        const csvContent = sessionManager.exportSessionToCSV(sessionId);\n        if (!csvContent) {\n            toast({\n                title: \"Error\",\n                description: \"Failed to export session\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        const session = sessionManager.loadSession(sessionId);\n        const blob = new Blob([\n            csvContent\n        ], {\n            type: 'text/csv;charset=utf-8;'\n        });\n        const link = document.createElement('a');\n        const url = URL.createObjectURL(blob);\n        link.setAttribute('href', url);\n        link.setAttribute('download', \"\".concat((session === null || session === void 0 ? void 0 : session.name) || 'session', \"_\").concat(new Date().toISOString().split('T')[0], \".csv\"));\n        link.style.visibility = 'hidden';\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        toast({\n            title: \"Export Complete\",\n            description: \"Session data has been exported to CSV\"\n        });\n    };\n    const formatRuntime = (runtime)=>{\n        if (!runtime || runtime < 0) return '0s';\n        const totalSeconds = Math.floor(runtime / 1000);\n        const hours = Math.floor(totalSeconds / 3600);\n        const minutes = Math.floor(totalSeconds % 3600 / 60);\n        const seconds = totalSeconds % 60;\n        if (hours > 0) {\n            return \"\".concat(hours, \"h \").concat(minutes, \"m \").concat(seconds, \"s\");\n        } else if (minutes > 0) {\n            return \"\".concat(minutes, \"m \").concat(seconds, \"s\");\n        } else {\n            return \"\".concat(seconds, \"s\");\n        }\n    };\n    const getCurrentSession = ()=>{\n        return sessions.find((s)=>s.id === currentSessionId);\n    };\n    const getActiveSessions = ()=>{\n        // Only show sessions that are actually currently running (bot is active)\n        // A session is considered \"active\" if:\n        // 1. It's marked as active AND\n        // 2. It's the current session for this window AND\n        // 3. The bot is actually running (botSystemStatus === 'Running')\n        return sessions.filter((s)=>{\n            const isCurrentSession = s.id === currentSessionId;\n            const isMarkedActive = s.isActive;\n            const isBotRunning = botSystemStatus === 'Running';\n            // Only show if it's the current session and bot is running\n            return isCurrentSession && isMarkedActive && isBotRunning;\n        });\n    };\n    const getInactiveSessions = ()=>{\n        return sessions.filter((s)=>!s.isActive);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"bg-card-foreground/5 border-border border-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                    lineNumber: 399,\n                                    columnNumber: 13\n                                }, this),\n                                \"Current Sessions\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                            lineNumber: 398,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                        lineNumber: 397,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: getActiveSessions().length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-5 gap-4 pb-2 border-b border-border text-sm font-medium text-muted-foreground\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: \"Session Name\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                            lineNumber: 408,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: \"Active Status\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                            lineNumber: 409,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: \"Runtime\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                            lineNumber: 410,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: \"Alarm\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                            lineNumber: 411,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: \"Actions\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                            lineNumber: 412,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                    lineNumber: 407,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: getActiveSessions().map((session)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-5 gap-4 items-center py-2 border-b border-border/50 last:border-b-0\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: editingSessionId === session.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-2 flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                value: editingName,\n                                                                onChange: (e)=>setEditingName(e.target.value),\n                                                                onKeyPress: (e)=>e.key === 'Enter' && handleRenameSession(session.id),\n                                                                className: \"text-sm\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 422,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                onClick: ()=>handleRenameSession(session.id),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 429,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 428,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                        lineNumber: 421,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: session.name\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 434,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"ghost\",\n                                                                onClick: ()=>{\n                                                                    setEditingSessionId(session.id);\n                                                                    setEditingName(session.name);\n                                                                },\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 443,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 435,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                    lineNumber: 419,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                        variant: \"default\",\n                                                        children: \"Active\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                        lineNumber: 450,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                    lineNumber: 449,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm\",\n                                                    children: session.id === currentSessionId ? formatRuntime(currentRuntime) : formatRuntime(session.runtime)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                    lineNumber: 455,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        size: \"sm\",\n                                                        variant: \"outline\",\n                                                        onClick: ()=>handleOpenAlarmConfig(session.id),\n                                                        title: \"Configure Alarms\",\n                                                        className: \"btn-outline-neo\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                            lineNumber: 467,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                        lineNumber: 460,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                    lineNumber: 459,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: session.id === currentSessionId ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        onClick: handleSaveCurrentSession,\n                                                        size: \"sm\",\n                                                        className: \"btn-neo\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"mr-2 h-3 w-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 474,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            \"Save\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                        lineNumber: 473,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>handleLoadSession(session.id),\n                                                                title: \"Load Session\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 480,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 479,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>handleExportSession(session.id),\n                                                                title: \"Export Session\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 483,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 482,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                        lineNumber: 478,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                    lineNumber: 471,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, session.id, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                            lineNumber: 418,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                    lineNumber: 416,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                            lineNumber: 405,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center text-muted-foreground py-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"h-12 w-12 mx-auto mb-4 opacity-50\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                    lineNumber: 494,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"No active session\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                    lineNumber: 495,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs\",\n                                    children: \"Start trading to create a session automatically\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                    lineNumber: 496,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                            lineNumber: 493,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                        lineNumber: 403,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                lineNumber: 396,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"bg-card-foreground/5 border-border border-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                        lineNumber: 508,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Past Sessions (\",\n                                    getInactiveSessions().length,\n                                    \")\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                lineNumber: 507,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                children: [\n                                    \"Auto-saved: \",\n                                    getInactiveSessions().length,\n                                    \" | Manual: 0\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                lineNumber: 511,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                        lineNumber: 506,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_6__.ScrollArea, {\n                            className: \"h-[400px]\",\n                            children: getInactiveSessions().length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center text-muted-foreground py-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"h-12 w-12 mx-auto mb-4 opacity-50\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                        lineNumber: 519,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"No saved sessions yet.\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                        lineNumber: 520,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs\",\n                                        children: \"Save your current session to get started.\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                        lineNumber: 521,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                lineNumber: 518,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-5 gap-4 pb-2 border-b border-border text-sm font-medium text-muted-foreground\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: \"Session Name\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                lineNumber: 527,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: \"Active Status\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                lineNumber: 528,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: \"Total Runtime\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                lineNumber: 529,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: \"Alarm\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                lineNumber: 530,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: \"Actions\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                lineNumber: 531,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                        lineNumber: 526,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: getInactiveSessions().map((session)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-5 gap-4 items-center py-2 border-b border-border/50 last:border-b-0\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: editingSessionId === session.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex gap-2 flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                    value: editingName,\n                                                                    onChange: (e)=>setEditingName(e.target.value),\n                                                                    onKeyPress: (e)=>e.key === 'Enter' && handleRenameSession(session.id),\n                                                                    className: \"text-sm\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 541,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                    size: \"sm\",\n                                                                    onClick: ()=>handleRenameSession(session.id),\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                        className: \"h-3 w-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                        lineNumber: 548,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 547,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                            lineNumber: 540,\n                                                            columnNumber: 27\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: session.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 553,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                    size: \"sm\",\n                                                                    variant: \"ghost\",\n                                                                    onClick: ()=>{\n                                                                        setEditingSessionId(session.id);\n                                                                        setEditingName(session.name);\n                                                                    },\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                        className: \"h-3 w-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                        lineNumber: 562,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 554,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                        lineNumber: 538,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                            variant: \"secondary\",\n                                                            children: \"Inactive\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                            lineNumber: 569,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                        lineNumber: 568,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm\",\n                                                        children: formatRuntime(sessionManager.getCurrentRuntime(session.id))\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                        lineNumber: 574,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            size: \"sm\",\n                                                            variant: \"outline\",\n                                                            onClick: ()=>handleOpenAlarmConfig(session.id),\n                                                            title: \"Configure Alarms\",\n                                                            className: \"btn-outline-neo\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"h-3 w-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 586,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                            lineNumber: 579,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                        lineNumber: 578,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>handleLoadSession(session.id),\n                                                                title: \"Load Session\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 592,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 591,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>handleExportSession(session.id),\n                                                                title: \"Export Session\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 595,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 594,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>handleDeleteSession(session.id),\n                                                                title: \"Delete Session\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 598,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 597,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                        lineNumber: 590,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, session.id, true, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                lineNumber: 537,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                        lineNumber: 535,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                lineNumber: 524,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                            lineNumber: 516,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                        lineNumber: 515,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                lineNumber: 505,\n                columnNumber: 7\n            }, this),\n            selectedSessionForAlarm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_SessionAlarmConfigModal__WEBPACK_IMPORTED_MODULE_10__.SessionAlarmConfigModal, {\n                isOpen: alarmModalOpen,\n                onClose: ()=>{\n                    setAlarmModalOpen(false);\n                    setSelectedSessionForAlarm(null);\n                },\n                sessionId: selectedSessionForAlarm.id,\n                sessionName: selectedSessionForAlarm.name,\n                currentAlarmSettings: selectedSessionForAlarm.alarmSettings,\n                onSave: handleSaveAlarmSettings\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                lineNumber: 612,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n        lineNumber: 394,\n        columnNumber: 5\n    }, this);\n}\n_s(SessionManager, \"qvvjKP1M0ciSaXHnASZ/CLpomBw=\", false, function() {\n    return [\n        _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_8__.useTradingContext,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__.useToast\n    ];\n});\n_c = SessionManager;\nvar _c;\n$RefreshReg$(_c, \"SessionManager\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/SessionManager.tsx\n"));

/***/ })

});