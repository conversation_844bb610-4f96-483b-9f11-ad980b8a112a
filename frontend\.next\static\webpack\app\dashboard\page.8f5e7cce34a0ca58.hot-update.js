"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/DashboardTabs.tsx":
/*!****************************************************!*\
  !*** ./src/components/dashboard/DashboardTabs.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardTabs)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./src/components/ui/tabs.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_History_ListOrdered_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=History,ListOrdered!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/list-ordered.js\");\n/* harmony import */ var _barrel_optimize_names_History_ListOrdered_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=History,ListOrdered!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/history.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst tabConfig = [\n    {\n        value: \"orders\",\n        label: \"Orders\",\n        href: \"/dashboard\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_History_ListOrdered_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardTabs.tsx\",\n            lineNumber: 9,\n            columnNumber: 65\n        }, undefined)\n    },\n    {\n        value: \"history\",\n        label: \"History\",\n        href: \"/dashboard/history\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_History_ListOrdered_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardTabs.tsx\",\n            lineNumber: 10,\n            columnNumber: 75\n        }, undefined)\n    }\n];\nfunction DashboardTabs() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    // Determine active tab based on pathname\n    let activeTabValue = \"orders\"; // Default\n    if (pathname === \"/dashboard/history\") activeTabValue = \"history\";\n    else if (pathname === \"/dashboard/analytics\") activeTabValue = \"analytics\";\n    const onTabChange = (value)=>{\n        const tab = tabConfig.find((t)=>t.value === value);\n        if (tab) {\n            router.push(tab.href);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_2__.Tabs, {\n        value: activeTabValue,\n        onValueChange: onTabChange,\n        className: \"w-full mb-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_2__.TabsList, {\n            className: \"grid w-full grid-cols-3 bg-card border-2 border-border\",\n            children: tabConfig.map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_2__.TabsTrigger, {\n                    value: tab.value,\n                    className: \"text-base data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=active]:font-bold\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            tab.icon,\n                            tab.label\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardTabs.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 13\n                    }, this)\n                }, tab.value, false, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardTabs.tsx\",\n                    lineNumber: 34,\n                    columnNumber: 11\n                }, this))\n        }, void 0, false, {\n            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardTabs.tsx\",\n            lineNumber: 32,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardTabs.tsx\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardTabs, \"gA9e4WsoP6a20xDgQgrFkfMP8lc=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname\n    ];\n});\n_c = DashboardTabs;\nvar _c;\n$RefreshReg$(_c, \"DashboardTabs\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/DashboardTabs.tsx\n"));

/***/ })

});