"use client";

import React from 'react';
import { useTradingContext } from '@/contexts/TradingContext';
import { TrendingUp } from 'lucide-react';

export default function MarketPriceDisplay() {
  const tradingContext = useTradingContext();

  // Add safety checks for context
  if (!tradingContext) {
    return (
      <div className="mb-4 p-3 bg-gradient-to-r from-green-500/10 to-primary/10 border border-border rounded-md">
        <div className="flex items-center justify-center gap-3">
          <span className="text-sm text-muted-foreground">Loading market data...</span>
        </div>
      </div>
    );
  }

  const { config, currentMarketPrice } = tradingContext;

  const formatPrice = (price: number | null | undefined) => {
    if (price === null || price === undefined || isNaN(price) || price <= 0) {
      return "0.00";
    }
    try {
      return price.toFixed(config?.numDigits || 2);
    } catch (error) {
      console.warn('Error formatting price:', error, 'Price:', price);
      return "0.00";
    }
  };

  // Check if both cryptos are selected
  const hasBothCryptos = config?.crypto1 && config?.crypto2;
  const displayPair = hasBothCryptos ? `${config.crypto1}/${config.crypto2}` : "Crypto 1/Crypto 2";
  const displayPrice = hasBothCryptos && currentMarketPrice && currentMarketPrice > 0 ? formatPrice(currentMarketPrice) : "0.00";

  return (
    <div className="mb-4 p-3 bg-gradient-to-r from-green-500/10 to-primary/10 border border-border rounded-md">
      <div className="flex items-center justify-center gap-3">
        <div className="flex items-center gap-2">
          <TrendingUp className="h-5 w-5 text-green-500" />
          <span className="text-sm font-medium text-muted-foreground">
            Current Market Price
          </span>
        </div>
        <div className="flex items-center gap-2">
          <span className="text-lg font-semibold text-foreground">
            {displayPair}:
          </span>
          <span className="text-2xl font-bold text-primary">
            ${displayPrice}
          </span>
        </div>
      </div>
    </div>
  );
}
