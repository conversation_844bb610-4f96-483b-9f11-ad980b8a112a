self.__RSC_SERVER_MANIFEST="{\n  \"node\": {\n    \"40e20b52d677633d10b6395882e3a5d6641f74c7b4\": {\n      \"workers\": {\n        \"app/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Cai%5C%5Cflows%5C%5Ctrading-mode-suggestion.ts%22%2C%5B%7B%22id%22%3A%2240e20b52d677633d10b6395882e3a5d6641f74c7b4%22%2C%22exportedName%22%3A%22suggestTradingMode%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": false\n        },\n        \"app/dashboard/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Cai%5C%5Cflows%5C%5Ctrading-mode-suggestion.ts%22%2C%5B%7B%22id%22%3A%2240e20b52d677633d10b6395882e3a5d6641f74c7b4%22%2C%22exportedName%22%3A%22suggestTradingMode%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": false\n        },\n        \"app/dashboard/history/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Cai%5C%5Cflows%5C%5Ctrading-mode-suggestion.ts%22%2C%5B%7B%22id%22%3A%2240e20b52d677633d10b6395882e3a5d6641f74c7b4%22%2C%22exportedName%22%3A%22suggestTradingMode%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": false\n        },\n        \"app/admin/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Cai%5C%5Cflows%5C%5Ctrading-mode-suggestion.ts%22%2C%5B%7B%22id%22%3A%2240e20b52d677633d10b6395882e3a5d6641f74c7b4%22%2C%22exportedName%22%3A%22suggestTradingMode%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/page\": \"action-browser\",\n        \"app/dashboard/page\": \"action-browser\",\n        \"app/dashboard/history/page\": \"action-browser\",\n        \"app/admin/page\": \"action-browser\"\n      }\n    }\n  },\n  \"edge\": {},\n  \"encryptionKey\": \"process.env.NEXT_SERVER_ACTIONS_ENCRYPTION_KEY\"\n}"