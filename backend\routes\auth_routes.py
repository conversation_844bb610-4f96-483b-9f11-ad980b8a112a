from flask import Blueprint, request, jsonify, make_response
from flask_jwt_extended import create_access_token, create_refresh_token, jwt_required, get_jwt_identity
from werkzeug.security import generate_password_hash, check_password_hash
from db import db
from models.user_model import User
import logging

logger = logging.getLogger(__name__)
auth_bp = Blueprint('auth', __name__, url_prefix='/auth')

# Helper function to add CORS headers to all responses
def add_cors_headers(response):
    response.headers.add('Access-Control-Allow-Origin', '*')
    response.headers.add('Access-Control-Allow-Headers', 'Content-Type,Authorization')
    response.headers.add('Access-Control-Allow-Methods', 'GET,PUT,POST,DELETE,OPTIONS')
    return response

@auth_bp.route('/register', methods=['POST', 'OPTIONS'])
def register():
    """Register a new user."""
    # Handle preflight OPTIONS request
    if request.method == 'OPTIONS':
        response = make_response()
        return add_cors_headers(response)
        
    logger.debug("Register endpoint called with data: %s", request.get_data())
    
    if not request.is_json:
        logger.warning("Register request missing JSON")
        response = jsonify({"error": "Missing JSON in request"})
        return add_cors_headers(response), 400
    
    data = request.get_json()
    username = data.get('username', None)
    password = data.get('password', None)
    email = data.get('email', None)
    
    logger.info(f"Attempting to register new user: {username}, {email}")
    
    if not username or not password:
        logger.warning("Missing username or password in register request")
        response = jsonify({"error": "Missing username or password"})
        return add_cors_headers(response), 400
    
    # Check if user already exists
    existing_user = User.query.filter_by(username=username).first()
    if existing_user:
        logger.warning(f"User {username} already exists")
        response = jsonify({"error": "User already exists"})
        return add_cors_headers(response), 409
    
    if email:
        existing_email = User.query.filter_by(email=email).first()
        if existing_email:
            logger.warning(f"Email {email} already in use")
            response = jsonify({"error": "Email already in use"})
            return add_cors_headers(response), 409
    
    new_user = User(username=username, password=password, email=email)
    
    try:
        db.session.add(new_user)
        db.session.commit()
        logger.info(f"New user registered: {username}")
        
        # Create tokens
        access_token = create_access_token(identity=str(new_user.id))
        refresh_token = create_refresh_token(identity=str(new_user.id))
        
        response = jsonify({
            "message": "User registered successfully",
            "user": new_user.to_dict(),
            "access_token": access_token,
            "refresh_token": refresh_token
        })
        return add_cors_headers(response), 201
    except Exception as e:
        db.session.rollback()
        logger.error(f"Error registering user: {str(e)}")
        response = jsonify({"error": "Internal server error"})
        return add_cors_headers(response), 500


@auth_bp.route('/login', methods=['POST', 'OPTIONS'])
def login():
    """Login a user."""
    # Handle preflight OPTIONS request
    if request.method == 'OPTIONS':
        response = make_response()
        return add_cors_headers(response)
    
    logger.debug("Login endpoint called with data: %s", request.get_data())
    
    if not request.is_json:
        logger.warning("Login request missing JSON")
        response = jsonify({"error": "Missing JSON in request"})
        return add_cors_headers(response), 400
    
    data = request.get_json()
    username = data.get('username', None)
    password = data.get('password', None)
    
    logger.info(f"Login attempt for user: {username}")
    
    if not username or not password:
        logger.warning("Missing username or password in login request")
        response = jsonify({"error": "Missing username or password"})
        return add_cors_headers(response), 400
    
    user = User.query.filter_by(username=username).first()
    
    if not user or not user.verify_password(password):
        logger.warning(f"Invalid login credentials for user: {username}")
        response = jsonify({"error": "Invalid credentials"})
        return add_cors_headers(response), 401
    
    # Create tokens
    access_token = create_access_token(identity=str(user.id))
    refresh_token = create_refresh_token(identity=str(user.id))
    
    logger.info(f"User logged in: {username}")
    response = jsonify({
        "message": "Login successful",
        "user": user.to_dict(),
        "access_token": access_token,
        "refresh_token": refresh_token
    })
    return add_cors_headers(response), 200


@auth_bp.route('/refresh', methods=['POST'])
@jwt_required(refresh=True)
def refresh_token():
    """Refresh access token."""
    current_user_id = get_jwt_identity()
    new_access_token = create_access_token(identity=str(current_user_id))
    
    return jsonify({
        "message": "Token refreshed",
        "access_token": new_access_token
    }), 200


@auth_bp.route('/me', methods=['GET'])
@jwt_required()
def get_user_info():
    """Get current user information."""
    current_user_id = int(get_jwt_identity())
    user = User.query.get(current_user_id)
    
    if not user:
        return jsonify({"error": "User not found"}), 404
    
    return jsonify({
        "user": user.to_dict()
    }), 200 