#!/usr/bin/env python3
"""Migration script to add alarm_settings column to trading_sessions table."""

import sqlite3
import os

def add_alarm_settings_column():
    """Add alarm_settings column to trading_sessions table if it doesn't exist."""
    
    db_path = 'instance/pluto.db'
    if not os.path.exists(db_path):
        print(f"❌ Database file {db_path} not found")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if column already exists
        cursor.execute("PRAGMA table_info(trading_sessions)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'alarm_settings' in columns:
            print("✅ alarm_settings column already exists")
            return True
        
        # Add the column
        print("Adding alarm_settings column to trading_sessions table...")
        cursor.execute("""
            ALTER TABLE trading_sessions 
            ADD COLUMN alarm_settings TEXT
        """)
        
        conn.commit()
        print("✅ Successfully added alarm_settings column")
        
        # Verify the column was added
        cursor.execute("PRAGMA table_info(trading_sessions)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'alarm_settings' in columns:
            print("✅ Column addition verified")
            return True
        else:
            print("❌ Column addition failed")
            return False
            
    except Exception as e:
        print(f"❌ Error adding column: {str(e)}")
        return False
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    add_alarm_settings_column()
